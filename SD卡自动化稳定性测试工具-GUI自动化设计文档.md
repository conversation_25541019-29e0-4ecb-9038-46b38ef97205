# SD卡自动化稳定性测试工具 - GUI自动化设计文档

**版本:** 1.0  
**日期:** 2025年7月18日  
**技术方案:** C# + FlaUI GUI自动化  

---

## 1. 项目概述

### 1.1 技术背景
由于大部分第三方测试软件（如H2testw、CrystalDiskMark等）不提供完整的命令行接口支持，本项目采用**GUI自动化技术**来实现对这些工具的自动化控制。使用C#语言结合FlaUI框架，通过Windows UI Automation API来模拟用户操作，实现完全自动化的测试流程。

### 1.2 核心技术栈
- **开发语言:** C# (.NET 6.0+)
- **GUI框架:** WPF (Windows Presentation Foundation)
- **自动化框架:** FlaUI (基于Windows UI Automation)
- **配置管理:** JSON配置文件
- **日志框架:** NLog
- **单元测试:** xUnit + Moq

---

## 2. 系统架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    主应用程序 (WPF)                          │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │   UI控制层      │  │   业务逻辑层     │  │   数据访问层     │ │
│  │  (ViewModels)   │  │ (Services)      │  │ (Repositories)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                GUI自动化引擎 (FlaUI)                         │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │  应用程序控制器  │  │   结果解析器     │  │   报告生成器     │ │
│  │ (AppControllers)│  │ (ResultParsers) │  │(ReportGenerator)│ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    第三方测试工具                            │
│  H2testw | CrystalDiskMark | ATTO | HD Bench | HD Tune Pro  │
│                IOMeter | PassMark BurnInTest                │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心模块设计

#### 2.2.1 GUI自动化引擎
```csharp
// 核心自动化接口
public interface ITestToolController
{
    Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);
    bool IsToolAvailable();
    string GetToolVersion();
}

// 基础控制器抽象类
public abstract class BaseTestToolController : ITestToolController
{
    protected readonly ILogger _logger;
    protected readonly AutomationBase _automation;
    protected readonly TestToolConfig _toolConfig;
    
    public abstract Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);
    
    protected virtual async Task<Application> LaunchApplicationAsync(string executablePath, string arguments = "")
    {
        // 启动应用程序的通用逻辑
    }
    
    protected virtual async Task WaitForWindowAsync(string windowTitle, TimeSpan timeout)
    {
        // 等待窗口出现的通用逻辑
    }
}
```

#### 2.2.2 具体工具控制器实现
```csharp
// H2testw控制器示例
public class H2testwController : BaseTestToolController
{
    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        try
        {
            // 1. 启动H2testw应用程序
            var app = await LaunchApplicationAsync(_toolConfig.ExecutablePath);
            
            // 2. 获取主窗口
            var mainWindow = await WaitForWindowAsync("H2testw", TimeSpan.FromSeconds(30));
            
            // 3. 设置目标驱动器
            await SetTargetDriveAsync(mainWindow, config.TargetDrive);
            
            // 4. 配置测试参数
            await ConfigureTestParametersAsync(mainWindow, config);
            
            // 5. 开始测试
            await StartTestAsync(mainWindow);
            
            // 6. 监控测试进度
            var result = await MonitorTestProgressAsync(mainWindow, cancellationToken);
            
            // 7. 解析测试结果
            return await ParseTestResultAsync(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "H2testw测试执行失败");
            throw;
        }
    }
    
    private async Task SetTargetDriveAsync(Window window, string targetDrive)
    {
        // 使用FlaUI查找并操作驱动器选择控件
        var driveComboBox = window.FindFirstDescendant(cf => cf.ByControlType(ControlType.ComboBox));
        await driveComboBox.AsComboBox().SelectAsync(targetDrive);
    }
}
```

---

## 3. 详细技术实现

### 3.1 FlaUI集成架构

#### 3.1.1 自动化会话管理
```csharp
public class AutomationSessionManager : IDisposable
{
    private readonly AutomationBase _automation;
    private readonly Dictionary<string, Application> _runningApplications;
    
    public AutomationSessionManager()
    {
        // 优先使用UIA3，回退到UIA2
        _automation = new UIA3Automation() ?? new UIA2Automation();
        _runningApplications = new Dictionary<string, Application>();
    }
    
    public async Task<Application> LaunchApplicationAsync(string executablePath, string arguments = "")
    {
        var processStartInfo = new ProcessStartInfo
        {
            FileName = executablePath,
            Arguments = arguments,
            UseShellExecute = true
        };
        
        var app = Application.Launch(processStartInfo);
        _runningApplications[Path.GetFileNameWithoutExtension(executablePath)] = app;
        
        return app;
    }
}
```

#### 3.1.2 UI元素识别策略
```csharp
public class UIElementFinder
{
    public static async Task<AutomationElement> FindElementWithRetryAsync(
        AutomationElement parent, 
        ConditionFactory condition, 
        TimeSpan timeout,
        TimeSpan retryInterval = default)
    {
        if (retryInterval == default) retryInterval = TimeSpan.FromMilliseconds(500);
        
        var endTime = DateTime.Now.Add(timeout);
        
        while (DateTime.Now < endTime)
        {
            var element = parent.FindFirstDescendant(condition);
            if (element != null) return element;
            
            await Task.Delay(retryInterval);
        }
        
        throw new ElementNotFoundException($"未能在{timeout}内找到指定UI元素");
    }
}
```

### 3.2 测试工具适配器设计

#### 3.2.1 CrystalDiskMark适配器
```csharp
public class CrystalDiskMarkController : BaseTestToolController
{
    private readonly CrystalDiskMarkConfig _config;
    
    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        var app = await LaunchApplicationAsync(_toolConfig.ExecutablePath);
        var mainWindow = await WaitForWindowAsync("CrystalDiskMark", TimeSpan.FromSeconds(30));
        
        // 设置测试参数
        await SetTestSizeAsync(mainWindow, config.TestSize);
        await SetTestCountAsync(mainWindow, config.TestCount);
        await SetTargetDriveAsync(mainWindow, config.TargetDrive);
        
        // 开始测试
        var startButton = await UIElementFinder.FindElementWithRetryAsync(
            mainWindow, 
            cf => cf.ByName("All").And(cf.ByControlType(ControlType.Button)),
            TimeSpan.FromSeconds(10));
            
        await startButton.AsButton().InvokeAsync();
        
        // 监控测试进度
        return await MonitorCrystalDiskMarkProgressAsync(mainWindow, cancellationToken);
    }
    
    private async Task<TestResult> MonitorCrystalDiskMarkProgressAsync(Window window, CancellationToken cancellationToken)
    {
        var progressBar = window.FindFirstDescendant(cf => cf.ByControlType(ControlType.ProgressBar));
        
        while (!cancellationToken.IsCancellationRequested)
        {
            var progress = progressBar.AsProgressBar().Value;
            
            // 报告进度
            OnProgressChanged?.Invoke(new ProgressEventArgs { Progress = progress });
            
            // 检查是否完成
            if (progress >= 100) break;
            
            await Task.Delay(1000, cancellationToken);
        }
        
        // 解析结果
        return await ParseCrystalDiskMarkResultAsync(window);
    }
}
```

### 3.3 结果解析引擎

#### 3.3.1 通用结果解析接口
```csharp
public interface IResultParser<T> where T : TestResult
{
    Task<T> ParseResultAsync(Window applicationWindow);
    Task<T> ParseResultFromFileAsync(string filePath);
    bool CanParseResult(string toolName);
}

public abstract class BaseResultParser<T> : IResultParser<T> where T : TestResult
{
    protected readonly ILogger _logger;
    
    public abstract Task<T> ParseResultAsync(Window applicationWindow);
    public abstract Task<T> ParseResultFromFileAsync(string filePath);
    public abstract bool CanParseResult(string toolName);
    
    protected virtual async Task<string> ExtractTextFromUIElementAsync(AutomationElement element)
    {
        // 从UI元素提取文本的通用方法
        return element.AsTextBox()?.Text ?? element.Name ?? string.Empty;
    }
}
```

#### 3.3.2 H2testw结果解析器
```csharp
public class H2testwResultParser : BaseResultParser<H2testwResult>
{
    public override async Task<H2testwResult> ParseResultAsync(Window applicationWindow)
    {
        var result = new H2testwResult();
        
        // 查找结果显示区域
        var resultTextBox = applicationWindow.FindFirstDescendant(
            cf => cf.ByControlType(ControlType.Edit).And(cf.ByName("Result")));
            
        if (resultTextBox != null)
        {
            var resultText = await ExtractTextFromUIElementAsync(resultTextBox);
            result = ParseH2testwText(resultText);
        }
        
        return result;
    }
    
    private H2testwResult ParseH2testwText(string resultText)
    {
        var result = new H2testwResult();
        
        // 解析写入速度
        var writeSpeedMatch = Regex.Match(resultText, @"Writing speed: ([\d.]+) MByte/s");
        if (writeSpeedMatch.Success)
        {
            result.WriteSpeed = double.Parse(writeSpeedMatch.Groups[1].Value);
        }
        
        // 解析读取速度
        var readSpeedMatch = Regex.Match(resultText, @"Reading speed: ([\d.]+) MByte/s");
        if (readSpeedMatch.Success)
        {
            result.ReadSpeed = double.Parse(readSpeedMatch.Groups[1].Value);
        }
        
        // 解析测试结果
        result.TestPassed = resultText.Contains("Test finished without errors");
        
        return result;
    }
}
```

---

## 4. 主应用程序设计

### 4.1 WPF主界面架构
```csharp
public class MainViewModel : ViewModelBase
{
    private readonly ITestOrchestrator _testOrchestrator;
    private readonly IConfigurationService _configService;
    private readonly IReportService _reportService;
    
    public ObservableCollection<DriveInfo> AvailableDrives { get; set; }
    public ObservableCollection<TestToolViewModel> TestTools { get; set; }
    public string CurrentStatus { get; set; }
    public double OverallProgress { get; set; }
    
    public ICommand StartTestCommand { get; }
    public ICommand StopTestCommand { get; }
    public ICommand RefreshDrivesCommand { get; }
    
    public async Task StartTestAsync()
    {
        var selectedTools = TestTools.Where(t => t.IsSelected).ToList();
        var selectedDrive = SelectedDrive;
        
        var testConfiguration = new TestConfiguration
        {
            TargetDrive = selectedDrive.Name,
            SelectedTools = selectedTools.Select(t => t.ToolName).ToList(),
            TestParameters = BuildTestParameters()
        };
        
        await _testOrchestrator.ExecuteTestSuiteAsync(testConfiguration, CancellationToken.None);
    }
}
```

### 4.2 测试编排器
```csharp
public class TestOrchestrator : ITestOrchestrator
{
    private readonly IEnumerable<ITestToolController> _controllers;
    private readonly IReportGenerator _reportGenerator;
    private readonly ILogger _logger;
    
    public async Task<TestSuiteResult> ExecuteTestSuiteAsync(
        TestConfiguration config, 
        CancellationToken cancellationToken)
    {
        var suiteResult = new TestSuiteResult
        {
            StartTime = DateTime.Now,
            Configuration = config,
            TestResults = new List<TestResult>()
        };
        
        foreach (var toolName in config.SelectedTools)
        {
            if (cancellationToken.IsCancellationRequested) break;
            
            var controller = _controllers.FirstOrDefault(c => c.GetType().Name.StartsWith(toolName));
            if (controller == null)
            {
                _logger.LogWarning($"未找到{toolName}的控制器");
                continue;
            }
            
            try
            {
                OnStatusChanged?.Invoke($"正在执行 {toolName} 测试...");
                
                var testResult = await controller.ExecuteTestAsync(config, cancellationToken);
                suiteResult.TestResults.Add(testResult);
                
                OnStatusChanged?.Invoke($"{toolName} 测试完成");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"{toolName} 测试失败");
                suiteResult.TestResults.Add(new TestResult 
                { 
                    ToolName = toolName, 
                    Success = false, 
                    ErrorMessage = ex.Message 
                });
            }
        }
        
        suiteResult.EndTime = DateTime.Now;
        
        // 生成报告
        await _reportGenerator.GenerateReportAsync(suiteResult);
        
        return suiteResult;
    }
}
```

---

## 5. 配置管理系统

### 5.1 配置文件结构
```json
{
  "TestTools": {
    "H2testw": {
      "ExecutablePath": "./third_party_tools/h2testw.exe",
      "WindowTitle": "H2testw",
      "DefaultParameters": {
        "TestAllSpace": true,
        "VerifyData": true
      },
      "Timeouts": {
        "LaunchTimeout": 30,
        "TestTimeout": 7200
      }
    },
    "CrystalDiskMark": {
      "ExecutablePath": "./third_party_tools/CrystalDiskMark.exe",
      "WindowTitle": "CrystalDiskMark",
      "DefaultParameters": {
        "TestSize": "1GiB",
        "TestCount": 5
      }
    }
  },
  "Reporting": {
    "OutputDirectory": "./Reports",
    "FileNameTemplate": "{DriveLabel}_{DateTime:yyyy-MM-dd_HH-mm-ss}_Report",
    "IncludeSystemInfo": true
  },
  "Logging": {
    "LogLevel": "Information",
    "LogDirectory": "./Logs"
  }
}
```

### 5.2 配置服务实现
```csharp
public class ConfigurationService : IConfigurationService
{
    private readonly string _configFilePath;
    private TestToolConfiguration _configuration;
    
    public async Task<TestToolConfiguration> LoadConfigurationAsync()
    {
        if (!File.Exists(_configFilePath))
        {
            _configuration = CreateDefaultConfiguration();
            await SaveConfigurationAsync(_configuration);
        }
        else
        {
            var json = await File.ReadAllTextAsync(_configFilePath);
            _configuration = JsonSerializer.Deserialize<TestToolConfiguration>(json);
        }
        
        return _configuration;
    }
    
    private TestToolConfiguration CreateDefaultConfiguration()
    {
        return new TestToolConfiguration
        {
            TestTools = new Dictionary<string, TestToolConfig>
            {
                ["H2testw"] = new TestToolConfig
                {
                    ExecutablePath = "./third_party_tools/h2testw.exe",
                    WindowTitle = "H2testw",
                    DefaultParameters = new Dictionary<string, object>
                    {
                        ["TestAllSpace"] = true,
                        ["VerifyData"] = true
                    }
                }
            }
        };
    }
}
```

---

## 6. 错误处理与恢复机制

### 6.1 异常处理策略
```csharp
public class RobustTestController : BaseTestToolController
{
    private readonly int _maxRetryAttempts = 3;
    private readonly TimeSpan _retryDelay = TimeSpan.FromSeconds(5);
    
    public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
    {
        for (int attempt = 1; attempt <= _maxRetryAttempts; attempt++)
        {
            try
            {
                return await ExecuteTestInternalAsync(config, cancellationToken);
            }
            catch (Exception ex) when (attempt < _maxRetryAttempts)
            {
                _logger.LogWarning($"测试执行失败，第{attempt}次重试: {ex.Message}");
                
                // 清理可能残留的进程
                await CleanupProcessesAsync();
                
                await Task.Delay(_retryDelay, cancellationToken);
            }
        }
        
        // 最后一次尝试，不捕获异常
        return await ExecuteTestInternalAsync(config, cancellationToken);
    }
    
    private async Task CleanupProcessesAsync()
    {
        // 强制关闭可能卡住的测试工具进程
        var processesToKill = new[] { "h2testw", "CrystalDiskMark", "ATTO", "HDTune" };
        
        foreach (var processName in processesToKill)
        {
            var processes = Process.GetProcessesByName(processName);
            foreach (var process in processes)
            {
                try
                {
                    process.Kill();
                    await process.WaitForExitAsync();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"无法终止进程 {processName}: {ex.Message}");
                }
            }
        }
    }
}
```

---

## 7. 项目结构

```
MassStorageStableTestTool/
├── src/
│   ├── MassStorageStableTestTool.Core/          # 核心业务逻辑
│   │   ├── Interfaces/                          # 接口定义
│   │   ├── Models/                              # 数据模型
│   │   ├── Services/                            # 业务服务
│   │   └── Exceptions/                          # 自定义异常
│   ├── MassStorageStableTestTool.Automation/    # GUI自动化引擎
│   │   ├── Controllers/                         # 工具控制器
│   │   ├── Parsers/                            # 结果解析器
│   │   ├── Common/                             # 通用自动化组件
│   │   └── Extensions/                         # FlaUI扩展
│   ├── MassStorageStableTestTool.UI/           # WPF用户界面
│   │   ├── Views/                              # 视图
│   │   ├── ViewModels/                         # 视图模型
│   │   ├── Controls/                           # 自定义控件
│   │   └── Converters/                         # 值转换器
│   └── MassStorageStableTestTool.Reports/      # 报告生成
│       ├── Generators/                         # 报告生成器
│       ├── Templates/                          # 报告模板
│       └── Exporters/                          # 导出器
├── tests/                                      # 单元测试
├── third_party_tools/                          # 第三方测试工具
├── config/                                     # 配置文件
├── docs/                                       # 文档
└── scripts/                                    # 构建脚本
```

---

## 8. 报告生成系统

### 8.1 报告生成器架构
```csharp
public interface IReportGenerator
{
    Task<string> GenerateReportAsync(TestSuiteResult suiteResult);
    Task<string> GenerateReportAsync(TestSuiteResult suiteResult, ReportFormat format);
    Task ExportReportAsync(string reportContent, string filePath, ReportFormat format);
}

public class ComprehensiveReportGenerator : IReportGenerator
{
    private readonly ISystemInfoService _systemInfoService;
    private readonly ITemplateEngine _templateEngine;

    public async Task<string> GenerateReportAsync(TestSuiteResult suiteResult)
    {
        var reportData = new ReportData
        {
            TestSuite = suiteResult,
            SystemInfo = await _systemInfoService.GetSystemInfoAsync(),
            GeneratedAt = DateTime.Now
        };

        var template = await LoadReportTemplateAsync("comprehensive_report.template");
        return await _templateEngine.RenderAsync(template, reportData);
    }
}
```

### 8.2 报告模板示例
```
===============================================
SD卡稳定性测试报告
===============================================

测试信息:
- 测试日期: {{GeneratedAt:yyyy-MM-dd HH:mm:ss}}
- 目标驱动器: {{TestSuite.Configuration.TargetDrive}}
- 测试工具: {{#each TestSuite.TestResults}}{{ToolName}}{{#unless @last}}, {{/unless}}{{/each}}

系统信息:
- 操作系统: {{SystemInfo.OperatingSystem}}
- 处理器: {{SystemInfo.Processor}}
- 内存: {{SystemInfo.TotalMemory}} GB

===============================================
测试结果汇总
===============================================

总体状态: {{#if TestSuite.AllTestsPassed}}通过{{else}}失败{{/if}}
测试时长: {{TestSuite.Duration}}

{{#each TestSuite.TestResults}}
--- {{ToolName}} ---
状态: {{#if Success}}通过{{else}}失败{{/if}}
{{#if Success}}
{{#if WriteSpeed}}写入速度: {{WriteSpeed}} MB/s{{/if}}
{{#if ReadSpeed}}读取速度: {{ReadSpeed}} MB/s{{/if}}
{{else}}
错误信息: {{ErrorMessage}}
{{/if}}

{{/each}}
```

---

## 9. 部署与分发

### 9.1 应用程序打包
```xml
<!-- MassStorageStableTestTool.UI.csproj -->
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net6.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="FlaUI.Core" Version="4.0.0" />
    <PackageReference Include="FlaUI.UIA3" Version="4.0.0" />
    <PackageReference Include="NLog" Version="5.2.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>
</Project>
```

### 9.2 安装包制作
```powershell
# 发布脚本 (publish.ps1)
param(
    [string]$Configuration = "Release",
    [string]$OutputPath = "./dist"
)

Write-Host "开始构建SD卡自动化测试工具..."

# 清理输出目录
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputPath

# 发布应用程序
dotnet publish src/MassStorageStableTestTool.UI/MassStorageStableTestTool.UI.csproj `
    -c $Configuration `
    -o "$OutputPath/app" `
    --self-contained true `
    -r win-x64

# 复制第三方工具
Copy-Item "third_party_tools" "$OutputPath/app/third_party_tools" -Recurse

# 复制配置文件
Copy-Item "config/appsettings.json" "$OutputPath/app/"

# 创建启动脚本
@"
@echo off
cd /d "%~dp0"
MassStorageStableTestTool.UI.exe
pause
"@ | Out-File "$OutputPath/app/启动测试工具.bat" -Encoding ASCII

Write-Host "构建完成! 输出目录: $OutputPath"
```

---

## 10. 测试策略

### 10.1 单元测试
```csharp
[Fact]
public async Task H2testwController_ExecuteTest_ShouldReturnValidResult()
{
    // Arrange
    var mockLogger = new Mock<ILogger<H2testwController>>();
    var mockConfig = new TestToolConfig
    {
        ExecutablePath = "test_h2testw.exe",
        WindowTitle = "H2testw"
    };

    var controller = new H2testwController(mockLogger.Object, mockConfig);
    var testConfig = new TestConfiguration
    {
        TargetDrive = "E:",
        TestSize = "1GB"
    };

    // Act
    var result = await controller.ExecuteTestAsync(testConfig, CancellationToken.None);

    // Assert
    Assert.NotNull(result);
    Assert.Equal("H2testw", result.ToolName);
}
```

### 10.2 集成测试
```csharp
[Fact]
public async Task TestOrchestrator_ExecuteFullTestSuite_ShouldGenerateReport()
{
    // Arrange
    var orchestrator = CreateTestOrchestrator();
    var config = new TestConfiguration
    {
        TargetDrive = "E:",
        SelectedTools = new[] { "H2testw", "CrystalDiskMark" }
    };

    // Act
    var result = await orchestrator.ExecuteTestSuiteAsync(config, CancellationToken.None);

    // Assert
    Assert.True(result.TestResults.Count >= 2);
    Assert.True(File.Exists(result.ReportFilePath));
}
```

---

## 11. 性能优化

### 11.1 UI响应性优化
```csharp
public class AsyncTestViewModel : ViewModelBase
{
    private readonly SemaphoreSlim _testSemaphore = new(1, 1);

    public async Task StartTestAsync()
    {
        if (!await _testSemaphore.WaitAsync(100))
        {
            ShowMessage("测试正在进行中，请稍候...");
            return;
        }

        try
        {
            IsTestRunning = true;

            // 在后台线程执行测试
            await Task.Run(async () =>
            {
                var progress = new Progress<ProgressEventArgs>(OnProgressChanged);
                await _testOrchestrator.ExecuteTestSuiteAsync(_configuration, _cancellationTokenSource.Token, progress);
            });
        }
        finally
        {
            IsTestRunning = false;
            _testSemaphore.Release();
        }
    }

    private void OnProgressChanged(ProgressEventArgs args)
    {
        // 切换到UI线程更新进度
        Application.Current.Dispatcher.Invoke(() =>
        {
            CurrentProgress = args.Progress;
            CurrentStatus = args.Status;
        });
    }
}
```

### 11.2 内存管理
```csharp
public class ResourceManagedController : BaseTestToolController, IDisposable
{
    private readonly List<IDisposable> _disposables = new();
    private bool _disposed = false;

    protected override async Task<Application> LaunchApplicationAsync(string executablePath, string arguments = "")
    {
        var app = await base.LaunchApplicationAsync(executablePath, arguments);
        _disposables.Add(app);
        return app;
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            foreach (var disposable in _disposables)
            {
                try
                {
                    disposable?.Dispose();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning($"资源释放时出现异常: {ex.Message}");
                }
            }

            _disposables.Clear();
            _disposed = true;
        }
    }
}
```

---

## 12. 安全考虑

### 12.1 权限管理
```csharp
public class SecurityService : ISecurityService
{
    public async Task<bool> ValidateExecutableAsync(string executablePath)
    {
        // 验证可执行文件的数字签名
        var fileInfo = new FileInfo(executablePath);
        if (!fileInfo.Exists) return false;

        // 检查文件是否在允许的目录中
        var allowedDirectories = new[]
        {
            Path.GetFullPath("./third_party_tools"),
            Environment.GetFolderPath(Environment.SpecialFolder.ProgramFiles),
            Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86)
        };

        var fullPath = Path.GetFullPath(executablePath);
        return allowedDirectories.Any(dir => fullPath.StartsWith(dir, StringComparison.OrdinalIgnoreCase));
    }

    public async Task<bool> CheckDriveAccessAsync(string driveLetter)
    {
        try
        {
            var drive = new DriveInfo(driveLetter);
            return drive.IsReady && drive.DriveType == DriveType.Removable;
        }
        catch
        {
            return false;
        }
    }
}
```

---

## 13. 监控与日志

### 13.1 日志配置
```xml
<!-- NLog.config -->
<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">

  <targets>
    <target xsi:type="File" name="fileTarget"
            fileName="logs/MassStorageTestTool-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />

    <target xsi:type="Console" name="consoleTarget"
            layout="${time} [${level}] ${message}" />
  </targets>

  <rules>
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
    <logger name="*" minlevel="Debug" writeTo="consoleTarget" />
  </rules>
</nlog>
```

### 13.2 性能监控
```csharp
public class PerformanceMonitor : IPerformanceMonitor
{
    private readonly ILogger _logger;
    private readonly PerformanceCounter _cpuCounter;
    private readonly PerformanceCounter _memoryCounter;

    public async Task<SystemPerformance> GetCurrentPerformanceAsync()
    {
        return new SystemPerformance
        {
            CpuUsage = _cpuCounter.NextValue(),
            MemoryUsage = _memoryCounter.NextValue(),
            Timestamp = DateTime.Now
        };
    }

    public async Task MonitorTestPerformanceAsync(CancellationToken cancellationToken)
    {
        while (!cancellationToken.IsCancellationRequested)
        {
            var performance = await GetCurrentPerformanceAsync();

            if (performance.CpuUsage > 90 || performance.MemoryUsage > 80)
            {
                _logger.LogWarning($"系统资源使用率过高: CPU {performance.CpuUsage:F1}%, 内存 {performance.MemoryUsage:F1}%");
            }

            await Task.Delay(5000, cancellationToken);
        }
    }
}
```

---

## 14. 总结

本设计文档详细描述了基于C# FlaUI的SD卡自动化稳定性测试工具的完整技术方案。主要特点包括：

### 14.1 技术优势
- **GUI自动化**: 使用FlaUI框架实现对第三方工具的完全自动化控制
- **模块化设计**: 采用清晰的分层架构，便于维护和扩展
- **错误恢复**: 完善的异常处理和重试机制，确保测试的可靠性
- **配置驱动**: 通过配置文件管理工具参数，无需重新编译

### 14.2 实施建议
1. **分阶段开发**: 建议先实现H2testw和CrystalDiskMark的自动化，验证技术可行性
2. **充分测试**: 在不同版本的测试工具上验证GUI自动化的稳定性
3. **用户培训**: 提供详细的用户手册和操作视频
4. **持续维护**: 建立版本管理机制，及时适配第三方工具的更新

### 14.3 风险评估
- **GUI变化风险**: 第三方工具界面更新可能影响自动化脚本
- **性能风险**: 长时间运行可能导致内存泄漏或系统资源耗尽
- **兼容性风险**: 不同Windows版本或系统配置可能影响自动化效果

通过本设计方案，可以实现一个稳定、可靠、易用的SD卡自动化测试工具，大幅提升测试效率和结果的一致性。
