﻿<Window x:Class="MassStorageStableTestTool.UI.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MassStorageStableTestTool.UI"
        xmlns:converters="clr-namespace:MassStorageStableTestTool.UI.Converters"
        mc:Ignorable="d"
        Title="SD卡自动化稳定性测试工具 v1.0"
        Height="900" Width="1400"
        MinHeight="800" MinWidth="1200"
        WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>

            <!-- 转换器 -->
            <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter"/>
            <converters:InverseBoolToVisibilityConverter x:Key="InverseBoolToVisibilityConverter"/>
            <converters:ProgressToStringConverter x:Key="ProgressToStringConverter"/>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="{StaticResource SurfaceColor}" BorderBrush="{StaticResource BorderColor}" BorderThickness="0,0,0,1">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建配置"/>
                <MenuItem Header="打开配置"/>
                <MenuItem Header="保存配置"/>
                <Separator/>
                <MenuItem Header="退出"/>
            </MenuItem>
            <MenuItem Header="设置(_S)">
                <MenuItem Header="测试工具设置" Command="{Binding OpenSettingsCommand}"/>
                <MenuItem Header="报告设置"/>
            </MenuItem>
            <MenuItem Header="报告(_R)">
                <MenuItem Header="查看最新报告"/>
                <MenuItem Header="打开报告文件夹"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="用户手册"/>
                <MenuItem Header="关于"/>
            </MenuItem>
        </Menu>

        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 上半部分：设备选择、工具选择、测试控制、进度显示 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="350"/>
                </Grid.ColumnDefinitions>

                <!-- 左侧：设备选择和测试控制 -->
                <StackPanel Grid.Column="0">
                    <!-- 设备选择卡片 -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <TextBlock Text="🖥️ 设备选择" Style="{StaticResource CardTitleStyle}"/>

                            <TextBlock Text="目标驱动器:" Style="{StaticResource LabelStyle}"/>
                            <ComboBox ItemsSource="{Binding AvailableDrives}"
                                    SelectedItem="{Binding SelectedDrive}"
                                    DisplayMemberPath="DisplayName"
                                    Style="{StaticResource ModernComboBoxStyle}"
                                    Margin="0,0,0,8"/>

                            <Grid Margin="0,0,0,8" Visibility="{Binding SelectedDrive, Converter={StaticResource BoolToVisibilityConverter}}">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="💾 容量:" Style="{StaticResource LabelStyle}"/>
                                    <TextBlock Text="{Binding SelectedDrive.TotalSizeFormatted}" FontWeight="Medium"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="📊 可用:" Style="{StaticResource LabelStyle}"/>
                                    <TextBlock Text="{Binding SelectedDrive.AvailableSpaceFormatted}" FontWeight="Medium"/>
                                </StackPanel>
                            </Grid>

                            <StackPanel Orientation="Horizontal" Visibility="{Binding SelectedDrive, Converter={StaticResource BoolToVisibilityConverter}}">
                                <TextBlock Text="🔄 状态:" Margin="0,0,4,0"/>
                                <TextBlock Text="{Binding SelectedDrive.Status}" FontWeight="Medium"/>
                            </StackPanel>

                            <Button Content="🔄 刷新驱动器"
                                  Command="{Binding RefreshDrivesCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  HorizontalAlignment="Left"
                                  Margin="0,12,0,0"/>
                        </StackPanel>
                    </Border>

                    <!-- 测试控制卡片 -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <TextBlock Text="🎮 测试控制" Style="{StaticResource CardTitleStyle}"/>

                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <Button Content="▶️ 开始测试"
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      Command="{Binding StartTestCommand}"
                                      IsEnabled="{Binding CanStartTest}"
                                      Margin="0,0,8,0"/>
                                <Button Content="⏹️ 停止测试"
                                      Style="{StaticResource SecondaryButtonStyle}"
                                      Command="{Binding StopTestCommand}"
                                      IsEnabled="{Binding IsTestRunning}"/>
                            </StackPanel>

                            <TextBlock Text="📋 测试配置:" FontWeight="Medium" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding TestSummary}"
                                     TextWrapping="Wrap"
                                     FontSize="12"
                                     Foreground="{StaticResource TextSecondaryColor}"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

                <!-- 中间：测试工具选择 -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,12">
                            <TextBlock Text="🔧 测试工具选择" Style="{StaticResource CardTitleStyle}" HorizontalAlignment="Left"/>
                            <Button Content="⚙️ 高级设置"
                                  Command="{Binding OpenSettingsCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  HorizontalAlignment="Right"/>
                        </Grid>

                        <ScrollViewer MaxHeight="400" Style="{StaticResource ModernScrollViewerStyle}">
                            <StackPanel>
                                <!-- GUI工具组 -->
                                <Expander Header="🖥️ GUI工具" IsExpanded="True" Style="{StaticResource ModernExpanderStyle}">
                                    <ItemsControl ItemsSource="{Binding GuiTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}"
                                                            Content="{Binding DisplayName}"
                                                            Style="{StaticResource ModernCheckBoxStyle}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8"
                                                           Fill="{Binding StatusColor}"
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>

                                <!-- CLI工具组 -->
                                <Expander Header="💻 CLI工具" IsExpanded="True" Style="{StaticResource ModernExpanderStyle}">
                                    <ItemsControl ItemsSource="{Binding CliTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}"
                                                            Content="{Binding DisplayName}"
                                                            Style="{StaticResource ModernCheckBoxStyle}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8"
                                                           Fill="{Binding StatusColor}"
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>

                                <!-- 混合工具组 -->
                                <Expander Header="🔄 混合工具" IsExpanded="False" Style="{StaticResource ModernExpanderStyle}">
                                    <ItemsControl ItemsSource="{Binding HybridTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}"
                                                            Content="{Binding DisplayName}"
                                                            Style="{StaticResource ModernCheckBoxStyle}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8"
                                                           Fill="{Binding StatusColor}"
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>

                <!-- 右侧：测试进度 -->
                <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="📊 测试进度" Style="{StaticResource CardTitleStyle}"/>

                        <TextBlock Text="{Binding CurrentStatus}" FontWeight="Medium" Margin="0,0,0,8"/>

                        <!-- 总体进度 -->
                        <TextBlock Text="总体进度:" Style="{StaticResource LabelStyle}"/>
                        <Grid Margin="0,0,0,12">
                            <ProgressBar Value="{Binding OverallProgress}"
                                       Style="{StaticResource ModernProgressBarStyle}"/>
                            <TextBlock Text="{Binding OverallProgress, Converter={StaticResource ProgressToStringConverter}}"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="11"
                                     FontWeight="Medium"/>
                        </Grid>

                        <!-- 当前测试进度 -->
                        <TextBlock Text="{Binding CurrentTestName, StringFormat=当前测试: {0}}"
                                 Style="{StaticResource LabelStyle}"
                                 Visibility="{Binding IsTestRunning, Converter={StaticResource BoolToVisibilityConverter}}"/>
                        <Grid Margin="0,0,0,12" Visibility="{Binding IsTestRunning, Converter={StaticResource BoolToVisibilityConverter}}">
                            <ProgressBar Value="{Binding CurrentTestProgress}"
                                       Height="16"
                                       Background="{StaticResource BorderColor}"
                                       Foreground="{StaticResource PrimaryColor}"/>
                            <TextBlock Text="{Binding CurrentTestProgress, Converter={StaticResource ProgressToStringConverter}}"
                                     HorizontalAlignment="Center"
                                     VerticalAlignment="Center"
                                     FontSize="10"/>
                        </Grid>

                        <!-- 任务状态列表 -->
                        <TextBlock Text="任务状态:" Style="{StaticResource LabelStyle}" FontWeight="Medium"/>
                        <ScrollViewer MaxHeight="200" Style="{StaticResource ModernScrollViewerStyle}">
                            <StackPanel>
                                <ItemsControl ItemsSource="{Binding GuiTools}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2"
                                                      Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <TextBlock Text="{Binding StatusIcon}" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding Name}" FontSize="11"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                                <ItemsControl ItemsSource="{Binding CliTools}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2"
                                                      Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <TextBlock Text="{Binding StatusIcon}" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding Name}" FontSize="11"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                                <ItemsControl ItemsSource="{Binding HybridTools}">
                                    <ItemsControl.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Orientation="Horizontal" Margin="0,2"
                                                      Visibility="{Binding IsSelected, Converter={StaticResource BoolToVisibilityConverter}}">
                                                <TextBlock Text="{Binding StatusIcon}" Margin="0,0,4,0"/>
                                                <TextBlock Text="{Binding Name}" FontSize="11"/>
                                            </StackPanel>
                                        </DataTemplate>
                                    </ItemsControl.ItemTemplate>
                                </ItemsControl>
                            </StackPanel>
                        </ScrollViewer>

                        <TextBlock Text="{Binding EstimatedTimeRemaining, StringFormat=预计剩余: {0}}"
                                 FontSize="12"
                                 Foreground="{StaticResource TextSecondaryColor}"
                                 Margin="0,8,0,0"
                                 Visibility="{Binding IsTestRunning, Converter={StaticResource BoolToVisibilityConverter}}"/>
                    </StackPanel>
                </Border>
            </Grid>

            <!-- 下半部分：实时日志 -->
            <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,16,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>

                    <Grid Grid.Row="0" Margin="0,0,0,8">
                        <TextBlock Text="📝 实时日志" Style="{StaticResource CardTitleStyle}" HorizontalAlignment="Left"/>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="清空"
                                  Command="{Binding ClearLogCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"
                                  Margin="0,0,8,0"/>
                            <Button Content="保存"
                                  Command="{Binding SaveLogCommand}"
                                  Style="{StaticResource SecondaryButtonStyle}"/>
                        </StackPanel>
                    </Grid>

                    <ScrollViewer Grid.Row="1"
                                Style="{StaticResource ModernScrollViewerStyle}"
                                x:Name="LogScrollViewer">
                        <ItemsControl ItemsSource="{Binding LogEntries}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" Margin="0,1">
                                        <TextBlock Text="{Binding LevelIcon}"
                                                 Margin="0,0,4,0"
                                                 VerticalAlignment="Top"/>
                                        <TextBlock Text="{Binding FormattedMessage}"
                                                 FontFamily="Consolas"
                                                 FontSize="11"
                                                 Foreground="{Binding LevelColor}"
                                                 TextWrapping="Wrap"
                                                 MaxWidth="1200"/>
                                    </StackPanel>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusBarText}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="CPU: "/>
                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0:F0}%}"/>
                </StackPanel>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="内存: "/>
                    <TextBlock Text="{Binding MemoryUsage, StringFormat={}{0:F0}%}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
