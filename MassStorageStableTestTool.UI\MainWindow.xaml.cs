using System.Windows;
using MassStorageStableTestTool.UI.ViewModels;
using Microsoft.Extensions.Logging;

namespace MassStorageStableTestTool.UI;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    public MainWindow(MainViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
    }

    public MainWindow() : this(new MainViewModel(new ConsoleLogger()))
    {
        // 设计时构造函数
    }
}

// 简单的控制台日志记录器，用于设计时
public class ConsoleLogger : ILogger<MainViewModel>
{
    public IDisposable? BeginScope<TState>(TState state) where TState : notnull => null;
    public bool IsEnabled(LogLevel logLevel) => true;
    public void Log<TState>(LogLevel logLevel, EventId eventId, TState state, Exception? exception, Func<TState, Exception?, string> formatter)
    {
        Console.WriteLine($"[{logLevel}] {formatter(state, exception)}");
    }
}