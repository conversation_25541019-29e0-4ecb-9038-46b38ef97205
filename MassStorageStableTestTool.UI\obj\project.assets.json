{"version": 3, "targets": {"net8.0-windows7.0": {"MassStorageStableTestTool.Automation/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"MassStorageStableTestTool.Core": "1.0.0"}, "compile": {"bin/placeholder/MassStorageStableTestTool.Automation.dll": {}}, "runtime": {"bin/placeholder/MassStorageStableTestTool.Automation.dll": {}}}, "MassStorageStableTestTool.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "compile": {"bin/placeholder/MassStorageStableTestTool.Core.dll": {}}, "runtime": {"bin/placeholder/MassStorageStableTestTool.Core.dll": {}}}, "MassStorageStableTestTool.Reports/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"MassStorageStableTestTool.Core": "1.0.0"}, "compile": {"bin/placeholder/MassStorageStableTestTool.Reports.dll": {}}, "runtime": {"bin/placeholder/MassStorageStableTestTool.Reports.dll": {}}}}}, "libraries": {"MassStorageStableTestTool.Automation/1.0.0": {"type": "project", "path": "../MassStorageStableTestTool.Automation/MassStorageStableTestTool.Automation.csproj", "msbuildProject": "../MassStorageStableTestTool.Automation/MassStorageStableTestTool.Automation.csproj"}, "MassStorageStableTestTool.Core/1.0.0": {"type": "project", "path": "../MassStorageStableTestTool.Core/MassStorageStableTestTool.Core.csproj", "msbuildProject": "../MassStorageStableTestTool.Core/MassStorageStableTestTool.Core.csproj"}, "MassStorageStableTestTool.Reports/1.0.0": {"type": "project", "path": "../MassStorageStableTestTool.Reports/MassStorageStableTestTool.Reports.csproj", "msbuildProject": "../MassStorageStableTestTool.Reports/MassStorageStableTestTool.Reports.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["MassStorageStableTestTool.Automation >= 1.0.0", "MassStorageStableTestTool.Core >= 1.0.0", "MassStorageStableTestTool.Reports >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.UI\\MassStorageStableTestTool.UI.csproj", "projectName": "MassStorageStableTestTool.UI", "projectPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.UI\\MassStorageStableTestTool.UI.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.UI\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Automation\\MassStorageStableTestTool.Automation.csproj": {"projectPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Automation\\MassStorageStableTestTool.Automation.csproj"}, "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\MassStorageStableTestTool.Core.csproj": {"projectPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Core\\MassStorageStableTestTool.Core.csproj"}, "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Reports\\MassStorageStableTestTool.Reports.csproj": {"projectPath": "C:\\Code\\MassStorageStableTestTool\\MassStorageStableTestTool.Reports\\MassStorageStableTestTool.Reports.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Host.win-x64", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[8.0.18, 8.0.18]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[8.0.18, 8.0.18]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.302/PortableRuntimeIdentifierGraph.json"}}}}