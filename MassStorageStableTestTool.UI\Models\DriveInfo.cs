using System.ComponentModel;

namespace MassStorageStableTestTool.UI.Models
{
    public enum DriveTestStatus
    {
        Ready,      // 就绪
        Selected,   // 已选择
        Testing,    // 测试中
        Completed,  // 测试完成
        Failed,     // 测试失败
        Skipped     // 跳过
    }

    public class DriveInfo : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _label = string.Empty;
        private long _totalSize;
        private long _availableSpace;
        private string _status = "就绪";
        private bool _isReady;
        private bool _isSelected;
        private DriveTestStatus _testStatus = DriveTestStatus.Ready;
        private double _testProgress;
        private string _currentTest = string.Empty;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public string Label
        {
            get => _label;
            set
            {
                _label = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public long TotalSize
        {
            get => _totalSize;
            set
            {
                _totalSize = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TotalSizeFormatted));
            }
        }

        public long AvailableSpace
        {
            get => _availableSpace;
            set
            {
                _availableSpace = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(AvailableSpaceFormatted));
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public bool IsReady
        {
            get => _isReady;
            set
            {
                _isReady = value;
                OnPropertyChanged();
            }
        }

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                _isSelected = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(SelectionStatusColor));
            }
        }

        public DriveTestStatus TestStatus
        {
            get => _testStatus;
            set
            {
                _testStatus = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TestStatusIcon));
                OnPropertyChanged(nameof(TestStatusColor));
            }
        }

        public double TestProgress
        {
            get => _testProgress;
            set
            {
                _testProgress = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TestProgressFormatted));
            }
        }

        public string CurrentTest
        {
            get => _currentTest;
            set
            {
                _currentTest = value;
                OnPropertyChanged();
            }
        }

        // 格式化显示属性
        public string DisplayName => string.IsNullOrEmpty(Label) ? $"{Name}" : $"{Name} [{Label}]";

        public string TotalSizeFormatted => FormatBytes(TotalSize);

        public string AvailableSpaceFormatted => FormatBytes(AvailableSpace);

        public string TestProgressFormatted => $"{TestProgress:F0}%";

        public string TestStatusIcon
        {
            get
            {
                return TestStatus switch
                {
                    DriveTestStatus.Ready => "⚪",
                    DriveTestStatus.Selected => "🟡",
                    DriveTestStatus.Testing => "🔄",
                    DriveTestStatus.Completed => "✅",
                    DriveTestStatus.Failed => "❌",
                    DriveTestStatus.Skipped => "⏭️",
                    _ => "⚪"
                };
            }
        }

        public System.Windows.Media.Brush TestStatusColor
        {
            get
            {
                return TestStatus switch
                {
                    DriveTestStatus.Ready => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),
                    DriveTestStatus.Selected => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Orange),
                    DriveTestStatus.Testing => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Blue),
                    DriveTestStatus.Completed => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green),
                    DriveTestStatus.Failed => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Red),
                    DriveTestStatus.Skipped => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray),
                    _ => new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Gray)
                };
            }
        }

        public System.Windows.Media.Brush SelectionStatusColor
        {
            get
            {
                return IsSelected
                    ? new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.Green)
                    : new System.Windows.Media.SolidColorBrush(System.Windows.Media.Colors.LightGray);
            }
        }

        private static string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:F1} {sizes[order]}";
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
