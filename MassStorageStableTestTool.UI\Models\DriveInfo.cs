using System.ComponentModel;

namespace MassStorageStableTestTool.UI.Models
{
    public class DriveInfo : INotifyPropertyChanged
    {
        private string _name = string.Empty;
        private string _label = string.Empty;
        private long _totalSize;
        private long _availableSpace;
        private string _status = "就绪";
        private bool _isReady;

        public string Name
        {
            get => _name;
            set
            {
                _name = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public string Label
        {
            get => _label;
            set
            {
                _label = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(DisplayName));
            }
        }

        public long TotalSize
        {
            get => _totalSize;
            set
            {
                _totalSize = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(TotalSizeFormatted));
            }
        }

        public long AvailableSpace
        {
            get => _availableSpace;
            set
            {
                _availableSpace = value;
                OnPropertyChanged();
                OnPropertyChanged(nameof(AvailableSpaceFormatted));
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged();
            }
        }

        public bool IsReady
        {
            get => _isReady;
            set
            {
                _isReady = value;
                OnPropertyChanged();
            }
        }

        // 格式化显示属性
        public string DisplayName => string.IsNullOrEmpty(Label) ? $"{Name}" : $"{Name} [{Label}]";
        
        public string TotalSizeFormatted => FormatBytes(TotalSize);
        
        public string AvailableSpaceFormatted => FormatBytes(AvailableSpace);

        private static string FormatBytes(long bytes)
        {
            if (bytes == 0) return "0 B";
            
            string[] sizes = { "B", "KB", "MB", "GB", "TB" };
            int order = 0;
            double size = bytes;
            
            while (size >= 1024 && order < sizes.Length - 1)
            {
                order++;
                size /= 1024;
            }
            
            return $"{size:F1} {sizes[order]}";
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
