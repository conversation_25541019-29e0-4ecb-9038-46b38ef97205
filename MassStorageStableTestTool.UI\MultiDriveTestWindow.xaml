<Window x:Class="MassStorageStableTestTool.UI.MultiDriveTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="SD卡自动化测试工具 - 多设备并行测试" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/AppStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>
    
    <Grid Background="{StaticResource BackgroundColor}">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{StaticResource PrimaryColor}" Padding="16,12">
            <Grid>
                <TextBlock Text="SD卡自动化稳定性测试工具 - 多设备并行测试" 
                         FontSize="18" 
                         FontWeight="Bold" 
                         Foreground="White"
                         HorizontalAlignment="Left"
                         VerticalAlignment="Center"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                    <TextBlock Text="v1.0" 
                             FontSize="12" 
                             Foreground="White" 
                             VerticalAlignment="Center"
                             Margin="0,0,16,0"/>
                    <Button Content="⚙️ 设置" 
                          Background="Transparent" 
                          Foreground="White" 
                          BorderThickness="1"
                          BorderBrush="White"
                          Padding="8,4"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- 主内容 -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="400"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="300"/>
            </Grid.ColumnDefinitions>
            
            <!-- 左侧：多设备选择 -->
            <Border Grid.Column="0" Style="{StaticResource CardStyle}" Margin="0,0,8,0">
                <StackPanel>
                    <Grid Margin="0,0,0,12">
                        <TextBlock Text="🖥️ 设备选择 (多选)" Style="{StaticResource CardTitleStyle}" HorizontalAlignment="Left"/>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="全选" Style="{StaticResource SecondaryButtonStyle}" FontSize="10" Padding="6,3" Margin="0,0,4,0"/>
                            <Button Content="全不选" Style="{StaticResource SecondaryButtonStyle}" FontSize="10" Padding="6,3"/>
                        </StackPanel>
                    </Grid>
                    
                    <!-- 模拟设备列表 -->
                    <ScrollViewer MaxHeight="350" Style="{StaticResource ModernScrollViewerStyle}">
                        <StackPanel>
                            <!-- 设备1 -->
                            <Border Background="{StaticResource SurfaceColor}" 
                                  BorderBrush="{StaticResource SuccessColor}" 
                                  BorderThickness="2" 
                                  CornerRadius="4" 
                                  Margin="0,2" 
                                  Padding="8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <Grid Grid.Row="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <CheckBox Grid.Column="0" IsChecked="True" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="E:\ [SanDisk Ultra 64GB]" FontWeight="Medium" FontSize="12"/>
                                            <TextBlock Text="59.6 GB" FontSize="10" Foreground="{StaticResource TextSecondaryColor}"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                                            <TextBlock Text="🔄" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="测试中" FontSize="10" Foreground="{StaticResource PrimaryColor}" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Grid>
                                    
                                    <Grid Grid.Row="1" Margin="0,4,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ProgressBar Grid.Column="0" Value="65" Height="12" Background="{StaticResource BorderColor}" Foreground="{StaticResource PrimaryColor}" Margin="0,0,8,0"/>
                                        <TextBlock Grid.Column="1" Text="65%" FontSize="10" VerticalAlignment="Center"/>
                                    </Grid>
                                    
                                    <TextBlock Grid.Row="2" Text="当前: CrystalDiskMark" FontSize="10" Foreground="{StaticResource TextSecondaryColor}" Margin="0,2,0,0"/>
                                </Grid>
                            </Border>
                            
                            <!-- 设备2 -->
                            <Border Background="{StaticResource SurfaceColor}" 
                                  BorderBrush="{StaticResource SuccessColor}" 
                                  BorderThickness="2" 
                                  CornerRadius="4" 
                                  Margin="0,2" 
                                  Padding="8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <Grid Grid.Row="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <CheckBox Grid.Column="0" IsChecked="True" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="F:\ [Kingston 32GB]" FontWeight="Medium" FontSize="12"/>
                                            <TextBlock Text="29.8 GB" FontSize="10" Foreground="{StaticResource TextSecondaryColor}"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                                            <TextBlock Text="✅" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="已完成" FontSize="10" Foreground="{StaticResource SuccessColor}" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Grid>
                                    
                                    <Grid Grid.Row="1" Margin="0,4,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <ProgressBar Grid.Column="0" Value="100" Height="12" Background="{StaticResource BorderColor}" Foreground="{StaticResource SuccessColor}" Margin="0,0,8,0"/>
                                        <TextBlock Grid.Column="1" Text="100%" FontSize="10" VerticalAlignment="Center"/>
                                    </Grid>
                                    
                                    <TextBlock Grid.Row="2" Text="当前: 已完成" FontSize="10" Foreground="{StaticResource TextSecondaryColor}" Margin="0,2,0,0"/>
                                </Grid>
                            </Border>
                            
                            <!-- 设备3 -->
                            <Border Background="{StaticResource SurfaceColor}" 
                                  BorderBrush="{StaticResource BorderColor}" 
                                  BorderThickness="2" 
                                  CornerRadius="4" 
                                  Margin="0,2" 
                                  Padding="8">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    
                                    <Grid Grid.Row="0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="Auto"/>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        
                                        <CheckBox Grid.Column="0" IsChecked="False" VerticalAlignment="Center" Margin="0,0,8,0"/>
                                        <StackPanel Grid.Column="1">
                                            <TextBlock Text="G:\ [Lexar 16GB]" FontWeight="Medium" FontSize="12"/>
                                            <TextBlock Text="14.9 GB" FontSize="10" Foreground="{StaticResource TextSecondaryColor}"/>
                                        </StackPanel>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                                            <TextBlock Text="⚪" Margin="0,0,4,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="未选择" FontSize="10" Foreground="{StaticResource TextSecondaryColor}" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Grid>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </ScrollViewer>
                    
                    <Button Content="🔄 刷新设备列表" Style="{StaticResource SecondaryButtonStyle}" HorizontalAlignment="Left" Margin="0,12,0,0"/>
                    
                    <!-- 测试控制 -->
                    <Border Background="{StaticResource PrimaryLightColor}" CornerRadius="4" Padding="12" Margin="0,16,0,0">
                        <StackPanel>
                            <TextBlock Text="🎮 并行测试控制" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Button Content="▶️ 开始并行测试" Style="{StaticResource PrimaryButtonStyle}" HorizontalAlignment="Stretch" Margin="0,0,0,8"/>
                            <Button Content="⏹️ 停止所有测试" Style="{StaticResource SecondaryButtonStyle}" HorizontalAlignment="Stretch"/>
                            
                            <TextBlock Text="📋 测试配置:" FontWeight="Medium" FontSize="11" Margin="0,8,0,4"/>
                            <TextBlock Text="• 选中设备: 2 个" FontSize="10"/>
                            <TextBlock Text="• 选中工具: 3 个" FontSize="10"/>
                            <TextBlock Text="• 预计时间: 约 1.5 小时" FontSize="10"/>
                            <TextBlock Text="• 测试模式: 并行测试" FontSize="10"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>
            
            <!-- 中间：测试工具选择 -->
            <Border Grid.Column="1" Style="{StaticResource CardStyle}" Margin="4,0">
                <StackPanel>
                    <TextBlock Text="🔧 测试工具选择" Style="{StaticResource CardTitleStyle}"/>
                    
                    <ScrollViewer MaxHeight="500" Style="{StaticResource ModernScrollViewerStyle}">
                        <StackPanel>
                            <!-- GUI工具 -->
                            <Expander Header="🖥️ GUI工具" IsExpanded="True" Style="{StaticResource ModernExpanderStyle}">
                                <StackPanel Margin="16,8,0,0">
                                    <CheckBox Content="H2testw (完整性测试)" IsChecked="True" Style="{StaticResource ModernCheckBoxStyle}"/>
                                    <CheckBox Content="CrystalDiskMark (性能基准)" IsChecked="True" Style="{StaticResource ModernCheckBoxStyle}"/>
                                    <CheckBox Content="ATTO Disk Benchmark" IsChecked="False" Style="{StaticResource ModernCheckBoxStyle}"/>
                                </StackPanel>
                            </Expander>
                            
                            <!-- CLI工具 -->
                            <Expander Header="💻 CLI工具" IsExpanded="True" Style="{StaticResource ModernExpanderStyle}">
                                <StackPanel Margin="16,8,0,0">
                                    <CheckBox Content="fio (高性能I/O测试)" IsChecked="True" Style="{StaticResource ModernCheckBoxStyle}"/>
                                    <CheckBox Content="diskspd (Windows原生)" IsChecked="False" Style="{StaticResource ModernCheckBoxStyle}"/>
                                    <CheckBox Content="dd (基础读写)" IsChecked="False" Style="{StaticResource ModernCheckBoxStyle}"/>
                                </StackPanel>
                            </Expander>
                            
                            <!-- 混合工具 -->
                            <Expander Header="🔄 混合工具" IsExpanded="False" Style="{StaticResource ModernExpanderStyle}">
                                <StackPanel Margin="16,8,0,0">
                                    <CheckBox Content="HD Tune Pro" IsChecked="False" Style="{StaticResource ModernCheckBoxStyle}"/>
                                    <CheckBox Content="IOMeter" IsChecked="False" Style="{StaticResource ModernCheckBoxStyle}"/>
                                </StackPanel>
                            </Expander>
                        </StackPanel>
                    </ScrollViewer>
                    
                    <!-- 并行测试说明 -->
                    <Border Background="{StaticResource PrimaryLightColor}" CornerRadius="4" Padding="12" Margin="0,16,0,0">
                        <StackPanel>
                            <TextBlock Text="💡 并行测试说明:" FontWeight="Medium" FontSize="12" Margin="0,0,0,8"/>
                            <TextBlock Text="• 所有选中的设备将同时进行测试，大大节省时间" FontSize="11" TextWrapping="Wrap" Margin="0,0,0,4"/>
                            <TextBlock Text="• 每个设备独立运行选中的测试工具" FontSize="11" TextWrapping="Wrap" Margin="0,0,0,4"/>
                            <TextBlock Text="• 可以实时查看每个设备的测试进度和状态" FontSize="11" TextWrapping="Wrap" Margin="0,0,0,4"/>
                            <TextBlock Text="• 测试完成后会生成每个设备的独立报告" FontSize="11" TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </Border>
            
            <!-- 右侧：并行测试进度 -->
            <Border Grid.Column="2" Style="{StaticResource CardStyle}" Margin="8,0,0,0">
                <StackPanel>
                    <TextBlock Text="📊 并行测试进度" Style="{StaticResource CardTitleStyle}"/>
                    
                    <TextBlock Text="正在并行测试 2 个设备..." FontWeight="Medium" Margin="0,0,0,8"/>
                    
                    <!-- 总体进度 -->
                    <TextBlock Text="总体进度:" Style="{StaticResource LabelStyle}"/>
                    <Grid Margin="0,0,0,12">
                        <ProgressBar Value="82" Style="{StaticResource ModernProgressBarStyle}"/>
                        <TextBlock Text="82%" HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="11" FontWeight="Medium"/>
                    </Grid>
                    
                    <!-- 设备状态统计 -->
                    <TextBlock Text="设备状态统计:" Style="{StaticResource LabelStyle}" FontWeight="Medium"/>
                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        <StackPanel Grid.Column="0">
                            <TextBlock FontSize="10"><Run Text="🔄 测试中: "/><Run Text="1" FontWeight="Medium"/></TextBlock>
                            <TextBlock FontSize="10"><Run Text="✅ 已完成: "/><Run Text="1" FontWeight="Medium"/></TextBlock>
                        </StackPanel>
                        <StackPanel Grid.Column="1">
                            <TextBlock FontSize="10"><Run Text="⚪ 等待中: "/><Run Text="0" FontWeight="Medium"/></TextBlock>
                            <TextBlock FontSize="10"><Run Text="❌ 失败: "/><Run Text="0" FontWeight="Medium"/></TextBlock>
                        </StackPanel>
                    </Grid>
                    
                    <!-- 测试工具状态 -->
                    <TextBlock Text="测试工具状态:" Style="{StaticResource LabelStyle}" FontWeight="Medium"/>
                    <StackPanel Margin="0,0,0,12">
                        <StackPanel Orientation="Horizontal" Margin="0,2">
                            <TextBlock Text="✅" Margin="0,0,4,0"/>
                            <TextBlock Text="H2testw" FontSize="11"/>
                            <TextBlock Text=" (100%)" FontSize="10" Foreground="{StaticResource TextSecondaryColor}" Margin="4,0,0,0"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,2">
                            <TextBlock Text="🔄" Margin="0,0,4,0"/>
                            <TextBlock Text="CrystalDiskMark" FontSize="11"/>
                            <TextBlock Text=" (65%)" FontSize="10" Foreground="{StaticResource TextSecondaryColor}" Margin="4,0,0,0"/>
                        </StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,2">
                            <TextBlock Text="⚪" Margin="0,0,4,0"/>
                            <TextBlock Text="fio" FontSize="11"/>
                            <TextBlock Text=" (0%)" FontSize="10" Foreground="{StaticResource TextSecondaryColor}" Margin="4,0,0,0"/>
                        </StackPanel>
                    </StackPanel>
                    
                    <TextBlock Text="💡 提示: 每个设备独立运行测试" FontSize="10" Foreground="{StaticResource TextSecondaryColor}" TextWrapping="Wrap"/>
                </StackPanel>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Style="{StaticResource StatusBarStyle}">
            <StatusBarItem>
                <TextBlock Text="正在并行测试 2 个设备"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="CPU: 25%"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="内存: 60%"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock Text="预计剩余: 28 分钟"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
