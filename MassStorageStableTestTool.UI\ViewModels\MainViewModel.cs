using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MassStorageStableTestTool.UI.Models;
using System.Collections.ObjectModel;
using System.Windows.Input;
using Microsoft.Extensions.Logging;
using LogLevel = MassStorageStableTestTool.UI.Models.LogLevel;

namespace MassStorageStableTestTool.UI.ViewModels
{
    public partial class MainViewModel : ObservableObject
    {
        private readonly ILogger<MainViewModel> _logger;

        [ObservableProperty]
        private ObservableCollection<DriveInfo> _availableDrives = new();

        [ObservableProperty]
        private DriveInfo? _selectedDrive;

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _guiTools = new();

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _cliTools = new();

        [ObservableProperty]
        private ObservableCollection<TestToolViewModel> _hybridTools = new();

        [ObservableProperty]
        private ObservableCollection<LogEntry> _logEntries = new();

        [ObservableProperty]
        private string _currentStatus = "等待开始";

        [ObservableProperty]
        private double _overallProgress = 0;

        [ObservableProperty]
        private string _currentTestName = string.Empty;

        [ObservableProperty]
        private double _currentTestProgress = 0;

        [ObservableProperty]
        private string _estimatedTimeRemaining = string.Empty;

        [ObservableProperty]
        private bool _isTestRunning = false;

        [ObservableProperty]
        private string _statusBarText = "就绪";

        [ObservableProperty]
        private double _cpuUsage = 0;

        [ObservableProperty]
        private double _memoryUsage = 0;

        public MainViewModel(ILogger<MainViewModel> logger)
        {
            _logger = logger;
            InitializeTestTools();
            RefreshDrives();
        }

        public bool CanStartTest => SelectedDrive != null && !IsTestRunning && HasSelectedTools;

        public bool HasSelectedTools => 
            GuiTools.Any(t => t.IsSelected) || 
            CliTools.Any(t => t.IsSelected) || 
            HybridTools.Any(t => t.IsSelected);

        public string TestSummary
        {
            get
            {
                var selectedCount = GuiTools.Count(t => t.IsSelected) + 
                                  CliTools.Count(t => t.IsSelected) + 
                                  HybridTools.Count(t => t.IsSelected);
                
                return $"• 预计时间: 约 {EstimateTestTime()} 小时\n" +
                       $"• 选中工具: {selectedCount} 个\n" +
                       $"• 测试模式: 标准";
            }
        }

        [RelayCommand]
        private void RefreshDrives()
        {
            try
            {
                AvailableDrives.Clear();
                
                var drives = System.IO.DriveInfo.GetDrives()
                    .Where(d => d.DriveType == System.IO.DriveType.Removable && d.IsReady)
                    .Select(d => new DriveInfo
                    {
                        Name = d.Name,
                        Label = d.VolumeLabel,
                        TotalSize = d.TotalSize,
                        AvailableSpace = d.AvailableFreeSpace,
                        IsReady = d.IsReady,
                        Status = d.IsReady ? "就绪" : "未就绪"
                    });

                foreach (var drive in drives)
                {
                    AvailableDrives.Add(drive);
                }

                if (AvailableDrives.Any() && SelectedDrive == null)
                {
                    SelectedDrive = AvailableDrives.First();
                }

                AddLogEntry(LogLevel.Info, $"发现 {AvailableDrives.Count} 个可移动存储设备");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "刷新驱动器列表时出错");
                AddLogEntry(LogLevel.Error, $"刷新驱动器列表失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private async Task StartTestAsync()
        {
            if (!CanStartTest) return;

            try
            {
                IsTestRunning = true;
                CurrentStatus = "正在准备测试...";
                AddLogEntry(LogLevel.Info, "开始执行测试套件...");

                // 模拟测试执行
                await SimulateTestExecution();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "测试执行失败");
                AddLogEntry(LogLevel.Error, $"测试执行失败: {ex.Message}");
            }
            finally
            {
                IsTestRunning = false;
                CurrentStatus = "测试完成";
            }
        }

        [RelayCommand]
        private void StopTest()
        {
            if (!IsTestRunning) return;

            IsTestRunning = false;
            CurrentStatus = "测试已停止";
            AddLogEntry(LogLevel.Warning, "用户停止了测试");
        }

        [RelayCommand]
        private void OpenSettings()
        {
            try
            {
                var settingsWindow = new Views.SettingsWindow();
                var result = settingsWindow.ShowDialog();

                if (result == true)
                {
                    AddLogEntry(LogLevel.Info, "设置已保存");
                }
                else
                {
                    AddLogEntry(LogLevel.Info, "设置已取消");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "打开设置窗口失败");
                AddLogEntry(LogLevel.Error, $"打开设置窗口失败: {ex.Message}");
            }
        }

        [RelayCommand]
        private void ClearLog()
        {
            LogEntries.Clear();
            AddLogEntry(LogLevel.Info, "日志已清空");
        }

        [RelayCommand]
        private void SaveLog()
        {
            // TODO: 实现保存日志功能
            AddLogEntry(LogLevel.Info, "保存日志到文件");
        }

        private void InitializeTestTools()
        {
            // GUI工具
            GuiTools.Add(new TestToolViewModel
            {
                Name = "H2testw",
                DisplayName = "H2testw (完整性测试)",
                Description = "SD卡完整性和真实容量测试",
                ToolType = TestToolType.GUI,
                Status = TestToolStatus.Available
            });

            GuiTools.Add(new TestToolViewModel
            {
                Name = "CrystalDiskMark",
                DisplayName = "CrystalDiskMark (性能基准)",
                Description = "磁盘性能基准测试",
                ToolType = TestToolType.GUI,
                Status = TestToolStatus.Available
            });

            GuiTools.Add(new TestToolViewModel
            {
                Name = "ATTO",
                DisplayName = "ATTO Disk Benchmark",
                Description = "不同块大小的读写性能测试",
                ToolType = TestToolType.GUI,
                Status = TestToolStatus.NotAvailable
            });

            // CLI工具
            CliTools.Add(new TestToolViewModel
            {
                Name = "fio",
                DisplayName = "fio (高性能I/O测试)",
                Description = "灵活的I/O性能测试工具",
                ToolType = TestToolType.CLI,
                Status = TestToolStatus.Available
            });

            CliTools.Add(new TestToolViewModel
            {
                Name = "diskspd",
                DisplayName = "diskspd (Windows原生)",
                Description = "Microsoft官方磁盘性能测试工具",
                ToolType = TestToolType.CLI,
                Status = TestToolStatus.NotAvailable
            });

            // 混合工具
            HybridTools.Add(new TestToolViewModel
            {
                Name = "HDTunePro",
                DisplayName = "HD Tune Pro",
                Description = "综合磁盘测试工具",
                ToolType = TestToolType.Hybrid,
                Status = TestToolStatus.NotConfigured
            });
        }

        private async Task SimulateTestExecution()
        {
            var selectedTools = GuiTools.Concat(CliTools).Concat(HybridTools)
                .Where(t => t.IsSelected).ToList();

            if (!selectedTools.Any()) return;

            for (int i = 0; i < selectedTools.Count; i++)
            {
                var tool = selectedTools[i];
                CurrentTestName = tool.DisplayName;
                tool.Status = TestToolStatus.Testing;

                AddLogEntry(LogLevel.Info, $"{tool.Name} 测试开始 - 目标驱动器: {SelectedDrive?.Name}");

                // 模拟测试进度
                for (int progress = 0; progress <= 100; progress += 10)
                {
                    CurrentTestProgress = progress;
                    tool.Progress = progress;
                    OverallProgress = ((i * 100) + progress) / selectedTools.Count;

                    await Task.Delay(500); // 模拟测试时间

                    if (!IsTestRunning) return;
                }

                tool.Status = TestToolStatus.Completed;
                AddLogEntry(LogLevel.Info, $"{tool.Name} 测试完成");
            }

            OverallProgress = 100;
            AddLogEntry(LogLevel.Info, "所有测试完成，正在生成报告...");
        }

        private void AddLogEntry(LogLevel level, string message)
        {
            var entry = new LogEntry
            {
                Timestamp = DateTime.Now,
                Level = level,
                Message = message,
                Source = "MainViewModel"
            };

            LogEntries.Add(entry);

            // 限制日志条目数量
            while (LogEntries.Count > 1000)
            {
                LogEntries.RemoveAt(0);
            }
        }

        private string EstimateTestTime()
        {
            var selectedCount = GuiTools.Count(t => t.IsSelected) + 
                              CliTools.Count(t => t.IsSelected) + 
                              HybridTools.Count(t => t.IsSelected);
            
            return (selectedCount * 0.5).ToString("F1");
        }

        partial void OnSelectedDriveChanged(DriveInfo? value)
        {
            OnPropertyChanged(nameof(CanStartTest));
            OnPropertyChanged(nameof(TestSummary));
        }

        partial void OnIsTestRunningChanged(bool value)
        {
            OnPropertyChanged(nameof(CanStartTest));
        }
    }
}
