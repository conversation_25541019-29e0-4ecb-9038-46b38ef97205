# WPF界面实现示例代码

**版本:** 1.0  
**日期:** 2025年7月18日  

---

## 1. 主窗口XAML实现

### 1.1 MainWindow.xaml
```xml
<Window x:Class="MassStorageStableTestTool.UI.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:controls="clr-namespace:MassStorageStableTestTool.UI.Controls"
        mc:Ignorable="d"
        Title="SD卡自动化稳定性测试工具 v1.0" 
        Height="900" Width="1400" 
        MinHeight="800" MinWidth="1200"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <!-- 样式资源 -->
        <Style x:Key="CardStyle" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="BorderBrush" Value="#E0E0E0"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>
        
        <Style x:Key="PrimaryButtonStyle" TargetType="Button">
            <Setter Property="Background" Value="#2196F3"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="16,8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#1976D2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#1565C0"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Grid Background="#FAFAFA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 菜单栏 -->
        <Menu Grid.Row="0" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <MenuItem Header="文件(_F)">
                <MenuItem Header="新建配置" Command="{Binding NewConfigCommand}"/>
                <MenuItem Header="打开配置" Command="{Binding OpenConfigCommand}"/>
                <MenuItem Header="保存配置" Command="{Binding SaveConfigCommand}"/>
                <Separator/>
                <MenuItem Header="退出" Command="{Binding ExitCommand}"/>
            </MenuItem>
            <MenuItem Header="设置(_S)">
                <MenuItem Header="测试工具设置" Command="{Binding OpenSettingsCommand}"/>
                <MenuItem Header="报告设置" Command="{Binding OpenReportSettingsCommand}"/>
            </MenuItem>
            <MenuItem Header="报告(_R)">
                <MenuItem Header="查看最新报告" Command="{Binding ViewLatestReportCommand}"/>
                <MenuItem Header="打开报告文件夹" Command="{Binding OpenReportFolderCommand}"/>
            </MenuItem>
            <MenuItem Header="帮助(_H)">
                <MenuItem Header="用户手册" Command="{Binding OpenManualCommand}"/>
                <MenuItem Header="关于" Command="{Binding AboutCommand}"/>
            </MenuItem>
        </Menu>
        
        <!-- 主内容区域 -->
        <Grid Grid.Row="1" Margin="16">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 上半部分：设备选择、工具选择、测试控制、进度显示 -->
            <Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="300"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="350"/>
                </Grid.ColumnDefinitions>
                
                <!-- 左侧：设备选择和测试控制 -->
                <StackPanel Grid.Column="0">
                    <!-- 设备选择卡片 -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <TextBlock Text="🖥️ 设备选择" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                            
                            <TextBlock Text="目标驱动器:" Margin="0,0,0,4"/>
                            <ComboBox ItemsSource="{Binding AvailableDrives}" 
                                    SelectedItem="{Binding SelectedDrive}"
                                    DisplayMemberPath="DisplayName"
                                    Height="32" Margin="0,0,0,8"/>
                            
                            <Grid Margin="0,0,0,8">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>
                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="💾 容量:" FontSize="12" Foreground="#757575"/>
                                    <TextBlock Text="{Binding SelectedDrive.TotalSize}" FontWeight="Medium"/>
                                </StackPanel>
                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="📊 可用:" FontSize="12" Foreground="#757575"/>
                                    <TextBlock Text="{Binding SelectedDrive.AvailableSpace}" FontWeight="Medium"/>
                                </StackPanel>
                            </Grid>
                            
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🔄 状态:" Margin="0,0,4,0"/>
                                <TextBlock Text="{Binding SelectedDrive.Status}" FontWeight="Medium"/>
                            </StackPanel>
                            
                            <Button Content="🔄 刷新驱动器" 
                                  Command="{Binding RefreshDrivesCommand}"
                                  HorizontalAlignment="Left"
                                  Margin="0,12,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- 测试控制卡片 -->
                    <Border Style="{StaticResource CardStyle}">
                        <StackPanel>
                            <TextBlock Text="🎮 测试控制" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                            
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <Button Content="▶️ 开始测试" 
                                      Style="{StaticResource PrimaryButtonStyle}"
                                      Command="{Binding StartTestCommand}"
                                      IsEnabled="{Binding CanStartTest}"
                                      Margin="0,0,8,0"/>
                                <Button Content="⏹️ 停止测试" 
                                      Command="{Binding StopTestCommand}"
                                      IsEnabled="{Binding IsTestRunning}"/>
                            </StackPanel>
                            
                            <TextBlock Text="📋 测试配置:" FontWeight="Medium" Margin="0,0,0,4"/>
                            <TextBlock Text="{Binding TestSummary}" 
                                     TextWrapping="Wrap" 
                                     FontSize="12" 
                                     Foreground="#757575"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
                
                <!-- 中间：测试工具选择 -->
                <Border Grid.Column="1" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <Grid Margin="0,0,0,12">
                            <TextBlock Text="🔧 测试工具选择" FontSize="16" FontWeight="Medium" HorizontalAlignment="Left"/>
                            <Button Content="⚙️ 高级设置" 
                                  Command="{Binding OpenAdvancedSettingsCommand}"
                                  HorizontalAlignment="Right"/>
                        </Grid>
                        
                        <ScrollViewer MaxHeight="400" VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <!-- GUI工具组 -->
                                <Expander Header="🖥️ GUI工具" IsExpanded="True" Margin="0,0,0,8">
                                    <ItemsControl ItemsSource="{Binding GuiTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}" 
                                                            Content="{Binding DisplayName}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8" 
                                                           Fill="{Binding StatusColor}" 
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>
                                
                                <!-- CLI工具组 -->
                                <Expander Header="💻 CLI工具" IsExpanded="True" Margin="0,0,0,8">
                                    <ItemsControl ItemsSource="{Binding CliTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}" 
                                                            Content="{Binding DisplayName}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8" 
                                                           Fill="{Binding StatusColor}" 
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>
                                
                                <!-- 混合工具组 -->
                                <Expander Header="🔄 混合工具" IsExpanded="False">
                                    <ItemsControl ItemsSource="{Binding HybridTools}" Margin="16,8,0,0">
                                        <ItemsControl.ItemTemplate>
                                            <DataTemplate>
                                                <StackPanel Orientation="Horizontal" Margin="0,2">
                                                    <CheckBox IsChecked="{Binding IsSelected}" 
                                                            Content="{Binding DisplayName}"
                                                            VerticalAlignment="Center"/>
                                                    <Ellipse Width="8" Height="8" 
                                                           Fill="{Binding StatusColor}" 
                                                           Margin="8,0,0,0"
                                                           VerticalAlignment="Center"/>
                                                </StackPanel>
                                            </DataTemplate>
                                        </ItemsControl.ItemTemplate>
                                    </ItemsControl>
                                </Expander>
                            </StackPanel>
                        </ScrollViewer>
                    </StackPanel>
                </Border>
                
                <!-- 右侧：测试进度 -->
                <Border Grid.Column="2" Style="{StaticResource CardStyle}">
                    <StackPanel>
                        <TextBlock Text="📊 测试进度" FontSize="16" FontWeight="Medium" Margin="0,0,0,12"/>
                        
                        <TextBlock Text="{Binding CurrentStatus}" FontWeight="Medium" Margin="0,0,0,8"/>
                        
                        <!-- 总体进度 -->
                        <TextBlock Text="总体进度:" FontSize="12" Margin="0,0,0,4"/>
                        <Grid Margin="0,0,0,12">
                            <ProgressBar Value="{Binding OverallProgress}" 
                                       Height="20" 
                                       Background="#E0E0E0"
                                       Foreground="#4CAF50"/>
                            <TextBlock Text="{Binding OverallProgress, StringFormat={}{0:F0}%}" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"
                                     FontSize="11" 
                                     FontWeight="Medium"/>
                        </Grid>
                        
                        <!-- 当前测试进度 -->
                        <TextBlock Text="{Binding CurrentTestName, StringFormat=当前测试: {0}}" 
                                 FontSize="12" Margin="0,0,0,4"/>
                        <Grid Margin="0,0,0,12">
                            <ProgressBar Value="{Binding CurrentTestProgress}" 
                                       Height="16" 
                                       Background="#E0E0E0"
                                       Foreground="#2196F3"/>
                            <TextBlock Text="{Binding CurrentTestProgress, StringFormat={}{0:F0}%}" 
                                     HorizontalAlignment="Center" 
                                     VerticalAlignment="Center"
                                     FontSize="10"/>
                        </Grid>
                        
                        <!-- 任务状态列表 -->
                        <TextBlock Text="任务状态:" FontSize="12" FontWeight="Medium" Margin="0,0,0,4"/>
                        <ScrollViewer MaxHeight="200" VerticalScrollBarVisibility="Auto">
                            <ItemsControl ItemsSource="{Binding TaskStatuses}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <StackPanel Orientation="Horizontal" Margin="0,2">
                                            <TextBlock Text="{Binding StatusIcon}" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding TaskName}" FontSize="11"/>
                                        </StackPanel>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </ScrollViewer>
                        
                        <TextBlock Text="{Binding EstimatedTimeRemaining, StringFormat=预计剩余: {0}}" 
                                 FontSize="12" 
                                 Foreground="#757575" 
                                 Margin="0,8,0,0"/>
                    </StackPanel>
                </Border>
            </Grid>
            
            <!-- 下半部分：实时日志 -->
            <Border Grid.Row="1" Style="{StaticResource CardStyle}" Margin="0,16,0,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                    </Grid.RowDefinitions>
                    
                    <Grid Grid.Row="0" Margin="0,0,0,8">
                        <TextBlock Text="📝 实时日志" FontSize="16" FontWeight="Medium" HorizontalAlignment="Left"/>
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Right">
                            <Button Content="清空" Command="{Binding ClearLogCommand}" Margin="0,0,8,0"/>
                            <Button Content="保存" Command="{Binding SaveLogCommand}"/>
                        </StackPanel>
                    </Grid>
                    
                    <ScrollViewer Grid.Row="1" 
                                VerticalScrollBarVisibility="Auto" 
                                HorizontalScrollBarVisibility="Auto"
                                x:Name="LogScrollViewer">
                        <ItemsControl ItemsSource="{Binding LogEntries}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding FormattedMessage}" 
                                             FontFamily="Consolas" 
                                             FontSize="11" 
                                             Foreground="{Binding LevelColor}"
                                             Margin="0,1"
                                             TextWrapping="Wrap"/>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </ScrollViewer>
                </Grid>
            </Border>
        </Grid>
        
        <!-- 状态栏 -->
        <StatusBar Grid.Row="2" Background="White" BorderBrush="#E0E0E0" BorderThickness="0,1,0,0">
            <StatusBarItem>
                <TextBlock Text="{Binding StatusBarText}"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="CPU: "/>
                    <TextBlock Text="{Binding CpuUsage, StringFormat={}{0:F0}%}"/>
                </StackPanel>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock Text="内存: "/>
                    <TextBlock Text="{Binding MemoryUsage, StringFormat={}{0:F0}%}"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
```

这个WPF界面实现示例提供了完整的主窗口XAML代码，包含了所有主要功能区域的布局和样式定义，为开发团队提供了具体的实现参考。
