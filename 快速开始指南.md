# SD卡自动化测试工具 - 快速开始指南

**版本:** 1.0  
**日期:** 2025年7月18日  

---

## 1. 环境准备

### 1.1 开发环境要求
- **操作系统**: Windows 10/11 (64位)
- **开发工具**: Visual Studio 2022 Community/Professional
- **.NET版本**: .NET 6.0 或更高版本
- **内存**: 至少 8GB RAM
- **存储**: 至少 10GB 可用空间

### 1.2 必需的NuGet包
```xml
<PackageReference Include="FlaUI.Core" Version="4.0.0" />
<PackageReference Include="FlaUI.UIA3" Version="4.0.0" />
<PackageReference Include="FlaUI.UIA2" Version="4.0.0" />
<PackageReference Include="NLog" Version="5.2.0" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging.NLog" Version="5.2.0" />
```

---

## 2. 项目结构创建

### 2.1 创建解决方案
```powershell
# 创建解决方案
dotnet new sln -n MassStorageStableTestTool

# 创建项目
dotnet new classlib -n MassStorageStableTestTool.Core
dotnet new classlib -n MassStorageStableTestTool.Automation  
dotnet new wpf -n MassStorageStableTestTool.UI
dotnet new classlib -n MassStorageStableTestTool.Reports
dotnet new xunit -n MassStorageStableTestTool.Tests

# 添加项目到解决方案
dotnet sln add **/*.csproj
```

### 2.2 设置项目引用
```powershell
# UI项目引用其他项目
dotnet add MassStorageStableTestTool.UI reference MassStorageStableTestTool.Core
dotnet add MassStorageStableTestTool.UI reference MassStorageStableTestTool.Automation
dotnet add MassStorageStableTestTool.UI reference MassStorageStableTestTool.Reports

# Automation项目引用Core项目
dotnet add MassStorageStableTestTool.Automation reference MassStorageStableTestTool.Core

# Reports项目引用Core项目
dotnet add MassStorageStableTestTool.Reports reference MassStorageStableTestTool.Core

# Tests项目引用所有项目
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.Core
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.Automation
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.UI
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.Reports
```

---

## 3. 核心代码框架

### 3.1 基础接口定义 (Core项目)
```csharp
// ITestToolController.cs
using System;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Core.Interfaces
{
    public interface ITestToolController
    {
        string ToolName { get; }
        Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);
        bool IsToolAvailable();
        string GetToolVersion();
    }
    
    public class TestConfiguration
    {
        public string TargetDrive { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public TimeSpan Timeout { get; set; } = TimeSpan.FromHours(2);
    }
    
    public class TestResult
    {
        public string ToolName { get; set; }
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public Dictionary<string, object> Data { get; set; } = new();
        public string ErrorMessage { get; set; }
    }
}
```

### 3.2 FlaUI基础控制器 (Automation项目)
```csharp
// BaseTestToolController.cs
using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.UIA3;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Automation
{
    public abstract class BaseTestToolController : ITestToolController, IDisposable
    {
        protected readonly ILogger _logger;
        protected readonly TestToolConfig _config;
        protected AutomationBase _automation;
        protected Application _application;
        
        public abstract string ToolName { get; }
        
        protected BaseTestToolController(ILogger logger, TestToolConfig config)
        {
            _logger = logger;
            _config = config;
            _automation = new UIA3Automation();
        }
        
        public virtual bool IsToolAvailable()
        {
            return File.Exists(_config.ExecutablePath);
        }
        
        public virtual string GetToolVersion()
        {
            if (!IsToolAvailable()) return "未找到";
            
            var versionInfo = FileVersionInfo.GetVersionInfo(_config.ExecutablePath);
            return versionInfo.FileVersion ?? "未知版本";
        }
        
        public abstract Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);
        
        protected virtual async Task<Application> LaunchApplicationAsync(string arguments = "")
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = _config.ExecutablePath,
                Arguments = arguments,
                UseShellExecute = true
            };
            
            _application = Application.Launch(startInfo);
            _logger.LogInformation($"已启动 {ToolName}: PID {_application.ProcessId}");
            
            return _application;
        }
        
        protected virtual async Task<Window> WaitForMainWindowAsync(TimeSpan timeout)
        {
            var endTime = DateTime.Now.Add(timeout);
            
            while (DateTime.Now < endTime)
            {
                try
                {
                    var mainWindow = _application.GetMainWindow(_automation);
                    if (mainWindow != null)
                    {
                        _logger.LogInformation($"{ToolName} 主窗口已就绪");
                        return mainWindow;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug($"等待主窗口时出现异常: {ex.Message}");
                }
                
                await Task.Delay(500);
            }
            
            throw new TimeoutException($"等待 {ToolName} 主窗口超时");
        }
        
        public virtual void Dispose()
        {
            try
            {
                _application?.Close();
                _application?.Dispose();
                _automation?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"释放资源时出现异常: {ex.Message}");
            }
        }
    }
}
```

### 3.3 GUI控制器示例 - H2testw
```csharp
// H2testwController.cs
using FlaUI.Core.AutomationElements;
using FlaUI.Core.Definitions;
using Microsoft.Extensions.Logging;
using System;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Automation.GUI.Controllers
{
    public class H2testwController : GuiTestToolController
    {
        public override string ToolName => "H2testw";

        public H2testwController(ILogger<H2testwController> logger, TestToolConfig config)
            : base(logger, config)
        {
        }

        public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
        {
            var result = new TestResult
            {
                ToolName = ToolName,
                StartTime = DateTime.Now
            };

            try
            {
                // 1. 启动应用程序
                await LaunchApplicationAsync();
                var mainWindow = await WaitForMainWindowAsync(TimeSpan.FromSeconds(30));

                // 2. 设置目标驱动器
                await SetTargetDriveAsync(mainWindow, config.TargetDrive);

                // 3. 开始测试
                await StartTestAsync(mainWindow);

                // 4. 监控测试进度
                await MonitorTestProgressAsync(mainWindow, cancellationToken);

                // 5. 解析结果
                var testData = await ParseResultsAsync(mainWindow);
                result.Data = testData;
                result.Success = true;

                _logger.LogInformation($"{ToolName} 测试完成");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, $"{ToolName} 测试失败");
            }
            finally
            {
                result.EndTime = DateTime.Now;
            }

            return result;
        }

        // ... 其他GUI操作方法
    }
}
```

### 3.4 CLI控制器示例 - fio
```csharp
// FioController.cs
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Automation.CLI.Controllers
{
    public class FioController : CliTestToolController
    {
        public override string ToolName => "fio";

        public FioController(ILogger<FioController> logger, TestToolConfig config)
            : base(logger, config)
        {
        }

        public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
        {
            var result = new TestResult
            {
                ToolName = ToolName,
                StartTime = DateTime.Now
            };

            try
            {
                // 1. 构建命令参数
                var arguments = BuildFioArguments(config);
                _logger.LogInformation($"执行fio命令: {_toolConfig.ExecutablePath} {arguments}");

                // 2. 执行命令
                var progress = new Progress<string>(line =>
                {
                    _logger.LogDebug($"fio输出: {line}");
                    // 可以在这里解析实时进度
                });

                var processResult = await ExecuteCommandAsync(arguments, cancellationToken, progress);

                // 3. 解析结果
                if (processResult.ExitCode == 0)
                {
                    result.Data = ParseFioOutput(processResult.StandardOutput);
                    result.Success = true;
                    _logger.LogInformation($"{ToolName} 测试完成");
                }
                else
                {
                    result.Success = false;
                    result.ErrorMessage = processResult.StandardError;
                    _logger.LogError($"{ToolName} 测试失败: {processResult.StandardError}");
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, $"{ToolName} 测试失败");
            }
            finally
            {
                result.EndTime = DateTime.Now;
            }

            return result;
        }

        private string BuildFioArguments(TestConfiguration config)
        {
            var args = new StringBuilder();

            // 基本参数
            args.Append($"--name=sdcard_test ");
            args.Append($"--filename={config.TargetDrive}\\testfile ");
            args.Append($"--size=1G ");
            args.Append($"--rw=randrw ");
            args.Append($"--bs=4k ");
            args.Append($"--numjobs=1 ");
            args.Append($"--runtime=300 ");
            args.Append($"--output-format=json ");

            // 从配置中获取自定义参数
            if (config.Parameters.ContainsKey("BlockSize"))
            {
                args.Replace("--bs=4k", $"--bs={config.Parameters["BlockSize"]}");
            }

            if (config.Parameters.ContainsKey("TestSize"))
            {
                args.Replace("--size=1G", $"--size={config.Parameters["TestSize"]}");
            }

            return args.ToString();
        }

        private Dictionary<string, object> ParseFioOutput(string output)
        {
            var data = new Dictionary<string, object>();

            try
            {
                // 解析JSON格式的fio输出
                var jsonDoc = JsonDocument.Parse(output);
                var jobs = jsonDoc.RootElement.GetProperty("jobs")[0];

                // 读取性能数据
                var read = jobs.GetProperty("read");
                data["ReadIOPS"] = read.GetProperty("iops").GetDouble();
                data["ReadBandwidth"] = read.GetProperty("bw").GetDouble();
                data["ReadLatency"] = read.GetProperty("lat_ns").GetProperty("mean").GetDouble();

                // 写入性能数据
                var write = jobs.GetProperty("write");
                data["WriteIOPS"] = write.GetProperty("iops").GetDouble();
                data["WriteBandwidth"] = write.GetProperty("bw").GetDouble();
                data["WriteLatency"] = write.GetProperty("lat_ns").GetProperty("mean").GetDouble();
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"解析fio JSON输出时出现异常: {ex.Message}");
                // 如果JSON解析失败，尝试文本解析
                data = ParseFioTextOutput(output);
            }

            return data;
        }

        private Dictionary<string, object> ParseFioTextOutput(string output)
        {
            var data = new Dictionary<string, object>();
            var lines = output.Split('\n');

            foreach (var line in lines)
            {
                // 解析文本格式的关键指标
                if (line.Contains("read:"))
                {
                    var match = Regex.Match(line, @"BW=(\d+\.?\d*)(\w+)");
                    if (match.Success)
                    {
                        data["ReadBandwidth"] = double.Parse(match.Groups[1].Value);
                        data["ReadBandwidthUnit"] = match.Groups[2].Value;
                    }
                }
                else if (line.Contains("write:"))
                {
                    var match = Regex.Match(line, @"BW=(\d+\.?\d*)(\w+)");
                    if (match.Success)
                    {
                        data["WriteBandwidth"] = double.Parse(match.Groups[1].Value);
                        data["WriteBandwidthUnit"] = match.Groups[2].Value;
                    }
                }
            }

            return data;
        }
    }
}
```

---

## 4. 配置文件设置

### 4.1 appsettings.json
```json
{
  "TestTools": {
    "H2testw": {
      "Type": "GUI",
      "ExecutablePath": "./third_party_tools/h2testw.exe",
      "WindowTitle": "H2testw",
      "Timeout": "02:00:00",
      "DefaultParameters": {
        "TestAllSpace": true,
        "VerifyData": true
      }
    },
    "CrystalDiskMark": {
      "Type": "GUI",
      "ExecutablePath": "./third_party_tools/CrystalDiskMark.exe",
      "WindowTitle": "CrystalDiskMark",
      "Timeout": "00:30:00",
      "DefaultParameters": {
        "TestSize": "1GiB",
        "TestCount": 5
      }
    },
    "fio": {
      "Type": "CLI",
      "ExecutablePath": "./third_party_tools/fio.exe",
      "Timeout": "00:10:00",
      "DefaultParameters": {
        "BlockSize": "4k",
        "TestSize": "1G",
        "Runtime": 300,
        "ReadWriteRatio": "randrw",
        "OutputFormat": "json"
      }
    },
    "diskspd": {
      "Type": "CLI",
      "ExecutablePath": "./third_party_tools/diskspd.exe",
      "Timeout": "00:10:00",
      "DefaultParameters": {
        "BlockSize": "4K",
        "Duration": 300,
        "Threads": 4,
        "WritePercentage": 25
      }
    },
    "HDTunePro": {
      "Type": "Hybrid",
      "ExecutablePath": "./third_party_tools/HDTunePro.exe",
      "WindowTitle": "HD Tune Pro",
      "Timeout": "00:30:00",
      "DefaultParameters": {
        "UseCommandLine": false,
        "TestType": "benchmark"
      }
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
```

### 4.2 NLog.config
```xml
<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  
  <targets>
    <target xsi:type="File" name="fileTarget"
            fileName="logs/app-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />
  </targets>
  
  <rules>
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
  </rules>
</nlog>
```

---

## 5. 依赖注入配置

### 5.1 服务注册 (App.xaml.cs)
```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;

namespace MassStorageStableTestTool.UI
{
    public partial class App : Application
    {
        public IServiceProvider ServiceProvider { get; private set; }
        
        protected override void OnStartup(StartupEventArgs e)
        {
            var serviceCollection = new ServiceCollection();
            ConfigureServices(serviceCollection);
            ServiceProvider = serviceCollection.BuildServiceProvider();
            
            var mainWindow = ServiceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
            
            base.OnStartup(e);
        }
        
        private void ConfigureServices(IServiceCollection services)
        {
            // 配置
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();
            services.AddSingleton<IConfiguration>(configuration);
            
            // 日志
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddNLog();
            });
            
            // 业务服务
            services.AddSingleton<ITestOrchestrator, TestOrchestrator>();
            services.AddSingleton<IConfigurationService, ConfigurationService>();
            services.AddSingleton<IReportService, ReportService>();
            
            // GUI测试控制器
            services.AddTransient<H2testwController>();
            services.AddTransient<CrystalDiskMarkController>();
            services.AddTransient<AttoController>();

            // CLI测试控制器
            services.AddTransient<FioController>();
            services.AddTransient<DiskSpdController>();
            services.AddTransient<DdController>();

            // 混合模式控制器
            services.AddTransient<HdTuneProController>();
            services.AddTransient<IOMeterController>();

            // 控制器工厂
            services.AddSingleton<IControllerFactory, ControllerFactory>();
            
            // UI
            services.AddTransient<MainWindow>();
            services.AddTransient<MainViewModel>();
        }
    }
}
```

---

## 6. 控制器工厂实现

### 6.1 控制器工厂接口
```csharp
// IControllerFactory.cs
using MassStorageStableTestTool.Core.Interfaces;

namespace MassStorageStableTestTool.Core.Interfaces
{
    public interface IControllerFactory
    {
        ITestToolController CreateController(string toolName);
        IEnumerable<ITestToolController> GetAllControllers();
        IEnumerable<string> GetSupportedTools();
    }
}
```

### 6.2 控制器工厂实现
```csharp
// ControllerFactory.cs
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MassStorageStableTestTool.Core.Interfaces;
using MassStorageStableTestTool.Automation.GUI.Controllers;
using MassStorageStableTestTool.Automation.CLI.Controllers;
using MassStorageStableTestTool.Automation.Hybrid.Controllers;

namespace MassStorageStableTestTool.Automation.Common
{
    public class ControllerFactory : IControllerFactory
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<ControllerFactory> _logger;
        private readonly Dictionary<string, Type> _controllerTypes;

        public ControllerFactory(IServiceProvider serviceProvider, ILogger<ControllerFactory> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
            _controllerTypes = new Dictionary<string, Type>
            {
                // GUI控制器
                ["H2testw"] = typeof(H2testwController),
                ["CrystalDiskMark"] = typeof(CrystalDiskMarkController),
                ["ATTO"] = typeof(AttoController),

                // CLI控制器
                ["fio"] = typeof(FioController),
                ["diskspd"] = typeof(DiskSpdController),
                ["dd"] = typeof(DdController),

                // 混合控制器
                ["HDTunePro"] = typeof(HdTuneProController),
                ["IOMeter"] = typeof(IOMeterController)
            };
        }

        public ITestToolController CreateController(string toolName)
        {
            if (!_controllerTypes.ContainsKey(toolName))
            {
                _logger.LogWarning($"不支持的测试工具: {toolName}");
                return null;
            }

            var controllerType = _controllerTypes[toolName];
            var controller = _serviceProvider.GetService(controllerType) as ITestToolController;

            if (controller == null)
            {
                _logger.LogError($"无法创建控制器: {toolName}");
                return null;
            }

            _logger.LogDebug($"已创建控制器: {toolName} ({controller.ToolType})");
            return controller;
        }

        public IEnumerable<ITestToolController> GetAllControllers()
        {
            var controllers = new List<ITestToolController>();

            foreach (var toolName in _controllerTypes.Keys)
            {
                var controller = CreateController(toolName);
                if (controller != null)
                {
                    controllers.Add(controller);
                }
            }

            return controllers;
        }

        public IEnumerable<string> GetSupportedTools()
        {
            return _controllerTypes.Keys;
        }
    }
}
```

### 6.3 测试编排器更新
```csharp
// TestOrchestrator.cs
using MassStorageStableTestTool.Core.Interfaces;
using Microsoft.Extensions.Logging;

namespace MassStorageStableTestTool.Automation.Common
{
    public class TestOrchestrator : ITestOrchestrator
    {
        private readonly IControllerFactory _controllerFactory;
        private readonly IReportGenerator _reportGenerator;
        private readonly ILogger<TestOrchestrator> _logger;

        public TestOrchestrator(
            IControllerFactory controllerFactory,
            IReportGenerator reportGenerator,
            ILogger<TestOrchestrator> logger)
        {
            _controllerFactory = controllerFactory;
            _reportGenerator = reportGenerator;
            _logger = logger;
        }

        public async Task<TestSuiteResult> ExecuteTestSuiteAsync(
            TestConfiguration config,
            CancellationToken cancellationToken)
        {
            var suiteResult = new TestSuiteResult
            {
                StartTime = DateTime.Now,
                Configuration = config,
                TestResults = new List<TestResult>()
            };

            foreach (var toolName in config.SelectedTools)
            {
                if (cancellationToken.IsCancellationRequested) break;

                var controller = _controllerFactory.CreateController(toolName);
                if (controller == null)
                {
                    _logger.LogWarning($"跳过不支持的工具: {toolName}");
                    continue;
                }

                if (!controller.IsToolAvailable())
                {
                    _logger.LogWarning($"工具不可用: {toolName}");
                    suiteResult.TestResults.Add(new TestResult
                    {
                        ToolName = toolName,
                        Success = false,
                        ErrorMessage = "工具不可用"
                    });
                    continue;
                }

                try
                {
                    OnStatusChanged?.Invoke($"正在执行 {toolName} 测试... (类型: {controller.ToolType})");

                    var testResult = await controller.ExecuteTestAsync(config, cancellationToken);
                    suiteResult.TestResults.Add(testResult);

                    OnStatusChanged?.Invoke($"{toolName} 测试完成 - {(testResult.Success ? "成功" : "失败")}");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, $"{toolName} 测试失败");
                    suiteResult.TestResults.Add(new TestResult
                    {
                        ToolName = toolName,
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                }
            }

            suiteResult.EndTime = DateTime.Now;

            // 生成报告
            await _reportGenerator.GenerateReportAsync(suiteResult);

            return suiteResult;
        }

        public event Action<string> OnStatusChanged;
    }
}
```

---

## 7. 第一个测试

### 7.1 GUI控制器测试
```csharp
// H2testwControllerTests.cs
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using MassStorageStableTestTool.Automation.GUI.Controllers;

namespace MassStorageStableTestTool.Tests.GUI
{
    public class H2testwControllerTests
    {
        [Fact]
        public void IsToolAvailable_WhenFileExists_ReturnsTrue()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<H2testwController>>();
            var config = new TestToolConfig
            {
                ExecutablePath = "test_h2testw.exe",
                Type = "GUI"
            };

            // 创建测试文件
            File.WriteAllText("test_h2testw.exe", "test");

            var controller = new H2testwController(mockLogger.Object, config);

            // Act
            var result = controller.IsToolAvailable();

            // Assert
            Assert.True(result);
            Assert.Equal(TestToolType.GUI, controller.ToolType);
            Assert.Equal("H2testw", controller.ToolName);

            // Cleanup
            File.Delete("test_h2testw.exe");
        }
    }
}
```

### 7.2 CLI控制器测试
```csharp
// FioControllerTests.cs
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using MassStorageStableTestTool.Automation.CLI.Controllers;

namespace MassStorageStableTestTool.Tests.CLI
{
    public class FioControllerTests
    {
        [Fact]
        public void ToolType_ShouldBeCLI()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<FioController>>();
            var config = new TestToolConfig
            {
                ExecutablePath = "fio.exe",
                Type = "CLI"
            };

            var controller = new FioController(mockLogger.Object, config);

            // Act & Assert
            Assert.Equal(TestToolType.CLI, controller.ToolType);
            Assert.Equal("fio", controller.ToolName);
        }
    }
}
```

### 7.3 运行测试
```powershell
# 构建解决方案
dotnet build

# 运行所有测试
dotnet test

# 运行特定测试类别
dotnet test --filter "Category=GUI"
dotnet test --filter "Category=CLI"

# 运行应用程序
dotnet run --project MassStorageStableTestTool.UI

# 发布应用程序
dotnet publish MassStorageStableTestTool.UI -c Release -r win-x64 --self-contained
```

---

## 8. 调试技巧

### 8.1 GUI自动化调试
```csharp
// 启用详细日志
_logger.LogDebug($"查找元素: {elementName}");

// 使用FlaUI Inspector工具查看UI结构
// 下载地址: https://github.com/FlaUI/FlaUI/releases

// 添加重试机制
private async Task<AutomationElement> FindElementWithRetryAsync(
    AutomationElement parent, 
    Func<ConditionFactory, ConditionBase> condition,
    int maxRetries = 5)
{
    for (int i = 0; i < maxRetries; i++)
    {
        var element = parent.FindFirstDescendant(condition);
        if (element != null) return element;
        
        await Task.Delay(1000);
    }
    
    throw new ElementNotFoundException("元素未找到");
}
```

### 8.2 CLI调用调试
```csharp
// 启用进程输出日志
private async Task<ProcessResult> ExecuteCommandWithLoggingAsync(
    string arguments,
    CancellationToken cancellationToken)
{
    _logger.LogInformation($"执行命令: {_toolConfig.ExecutablePath} {arguments}");

    var processStartInfo = new ProcessStartInfo
    {
        FileName = _toolConfig.ExecutablePath,
        Arguments = arguments,
        UseShellExecute = false,
        RedirectStandardOutput = true,
        RedirectStandardError = true,
        CreateNoWindow = true
    };

    using var process = new Process { StartInfo = processStartInfo };
    var outputBuilder = new StringBuilder();
    var errorBuilder = new StringBuilder();

    process.OutputDataReceived += (sender, e) =>
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            _logger.LogDebug($"[STDOUT] {e.Data}");
            outputBuilder.AppendLine(e.Data);
        }
    };

    process.ErrorDataReceived += (sender, e) =>
    {
        if (!string.IsNullOrEmpty(e.Data))
        {
            _logger.LogWarning($"[STDERR] {e.Data}");
            errorBuilder.AppendLine(e.Data);
        }
    };

    process.Start();
    process.BeginOutputReadLine();
    process.BeginErrorReadLine();

    await process.WaitForExitAsync(cancellationToken);

    _logger.LogInformation($"命令执行完成，退出代码: {process.ExitCode}");

    return new ProcessResult
    {
        ExitCode = process.ExitCode,
        StandardOutput = outputBuilder.ToString(),
        StandardError = errorBuilder.ToString()
    };
}
```

### 8.3 常见问题解决

#### GUI自动化问题
1. **UI元素找不到**: 使用FlaUI Inspector检查元素属性
2. **应用程序启动失败**: 检查路径和权限
3. **测试超时**: 增加超时时间或优化查找逻辑
4. **内存泄漏**: 确保正确释放FlaUI资源

#### CLI调用问题
1. **命令执行失败**: 检查可执行文件路径和参数格式
2. **权限不足**: 确保有足够权限执行命令
3. **输出解析错误**: 验证输出格式和解析逻辑
4. **进程挂起**: 设置合适的超时时间

#### 混合模式问题
1. **模式选择错误**: 检查配置文件中的模式设置
2. **切换失败**: 确保两种模式都能正常工作
3. **结果不一致**: 验证两种模式的输出格式

---

## 9. 下一步

### 9.1 GUI控制器开发
1. **完善H2testw控制器**: 处理更多边界情况和异常
2. **添加CrystalDiskMark控制器**: 参考H2testw实现
3. **实现ATTO控制器**: 支持不同测试模式
4. **添加HD Bench控制器**: 处理复杂的GUI交互

### 9.2 CLI控制器开发
1. **完善fio控制器**: 支持更多测试场景和参数
2. **添加diskspd控制器**: 实现Windows原生性能测试
3. **实现dd控制器**: 支持基本的读写测试
4. **添加badblocks控制器**: 实现坏块检测功能

### 9.3 混合模式控制器
1. **实现HD Tune Pro控制器**: 支持GUI和CLI两种模式
2. **添加IOMeter控制器**: 处理复杂的配置文件
3. **完善模式选择逻辑**: 根据配置自动选择最佳模式

### 9.4 系统集成
1. **创建WPF主界面**: 实现统一的用户交互界面
2. **完善控制器工厂**: 支持动态加载和配置
3. **实现测试编排器**: 协调不同类型的控制器
4. **添加配置管理**: 支持动态配置修改和验证
5. **实现报告生成**: 整合GUI、CLI和混合模式的测试结果

### 9.5 质量保证
1. **编写全面的单元测试**: 覆盖所有控制器类型
2. **实现集成测试**: 验证端到端的测试流程
3. **添加性能测试**: 确保系统在长时间运行下的稳定性
4. **完善错误处理**: 提供友好的错误信息和恢复机制

这个更新后的快速开始指南提供了支持GUI、CLI和混合模式的完整框架，开发团队可以基于此快速开始开发工作，并根据实际需求选择最适合的自动化方式。
