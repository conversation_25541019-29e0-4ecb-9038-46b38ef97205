# SD卡自动化测试工具 - 快速开始指南

**版本:** 1.0  
**日期:** 2025年7月18日  

---

## 1. 环境准备

### 1.1 开发环境要求
- **操作系统**: Windows 10/11 (64位)
- **开发工具**: Visual Studio 2022 Community/Professional
- **.NET版本**: .NET 6.0 或更高版本
- **内存**: 至少 8GB RAM
- **存储**: 至少 10GB 可用空间

### 1.2 必需的NuGet包
```xml
<PackageReference Include="FlaUI.Core" Version="4.0.0" />
<PackageReference Include="FlaUI.UIA3" Version="4.0.0" />
<PackageReference Include="FlaUI.UIA2" Version="4.0.0" />
<PackageReference Include="NLog" Version="5.2.0" />
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging" Version="7.0.0" />
<PackageReference Include="Microsoft.Extensions.Logging.NLog" Version="5.2.0" />
```

---

## 2. 项目结构创建

### 2.1 创建解决方案
```powershell
# 创建解决方案
dotnet new sln -n MassStorageStableTestTool

# 创建项目
dotnet new classlib -n MassStorageStableTestTool.Core
dotnet new classlib -n MassStorageStableTestTool.Automation  
dotnet new wpf -n MassStorageStableTestTool.UI
dotnet new classlib -n MassStorageStableTestTool.Reports
dotnet new xunit -n MassStorageStableTestTool.Tests

# 添加项目到解决方案
dotnet sln add **/*.csproj
```

### 2.2 设置项目引用
```powershell
# UI项目引用其他项目
dotnet add MassStorageStableTestTool.UI reference MassStorageStableTestTool.Core
dotnet add MassStorageStableTestTool.UI reference MassStorageStableTestTool.Automation
dotnet add MassStorageStableTestTool.UI reference MassStorageStableTestTool.Reports

# Automation项目引用Core项目
dotnet add MassStorageStableTestTool.Automation reference MassStorageStableTestTool.Core

# Reports项目引用Core项目
dotnet add MassStorageStableTestTool.Reports reference MassStorageStableTestTool.Core

# Tests项目引用所有项目
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.Core
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.Automation
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.UI
dotnet add MassStorageStableTestTool.Tests reference MassStorageStableTestTool.Reports
```

---

## 3. 核心代码框架

### 3.1 基础接口定义 (Core项目)
```csharp
// ITestToolController.cs
using System;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Core.Interfaces
{
    public interface ITestToolController
    {
        string ToolName { get; }
        Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);
        bool IsToolAvailable();
        string GetToolVersion();
    }
    
    public class TestConfiguration
    {
        public string TargetDrive { get; set; }
        public Dictionary<string, object> Parameters { get; set; } = new();
        public TimeSpan Timeout { get; set; } = TimeSpan.FromHours(2);
    }
    
    public class TestResult
    {
        public string ToolName { get; set; }
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public Dictionary<string, object> Data { get; set; } = new();
        public string ErrorMessage { get; set; }
    }
}
```

### 3.2 FlaUI基础控制器 (Automation项目)
```csharp
// BaseTestToolController.cs
using FlaUI.Core;
using FlaUI.Core.AutomationElements;
using FlaUI.UIA3;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Automation
{
    public abstract class BaseTestToolController : ITestToolController, IDisposable
    {
        protected readonly ILogger _logger;
        protected readonly TestToolConfig _config;
        protected AutomationBase _automation;
        protected Application _application;
        
        public abstract string ToolName { get; }
        
        protected BaseTestToolController(ILogger logger, TestToolConfig config)
        {
            _logger = logger;
            _config = config;
            _automation = new UIA3Automation();
        }
        
        public virtual bool IsToolAvailable()
        {
            return File.Exists(_config.ExecutablePath);
        }
        
        public virtual string GetToolVersion()
        {
            if (!IsToolAvailable()) return "未找到";
            
            var versionInfo = FileVersionInfo.GetVersionInfo(_config.ExecutablePath);
            return versionInfo.FileVersion ?? "未知版本";
        }
        
        public abstract Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken);
        
        protected virtual async Task<Application> LaunchApplicationAsync(string arguments = "")
        {
            var startInfo = new ProcessStartInfo
            {
                FileName = _config.ExecutablePath,
                Arguments = arguments,
                UseShellExecute = true
            };
            
            _application = Application.Launch(startInfo);
            _logger.LogInformation($"已启动 {ToolName}: PID {_application.ProcessId}");
            
            return _application;
        }
        
        protected virtual async Task<Window> WaitForMainWindowAsync(TimeSpan timeout)
        {
            var endTime = DateTime.Now.Add(timeout);
            
            while (DateTime.Now < endTime)
            {
                try
                {
                    var mainWindow = _application.GetMainWindow(_automation);
                    if (mainWindow != null)
                    {
                        _logger.LogInformation($"{ToolName} 主窗口已就绪");
                        return mainWindow;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogDebug($"等待主窗口时出现异常: {ex.Message}");
                }
                
                await Task.Delay(500);
            }
            
            throw new TimeoutException($"等待 {ToolName} 主窗口超时");
        }
        
        public virtual void Dispose()
        {
            try
            {
                _application?.Close();
                _application?.Dispose();
                _automation?.Dispose();
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"释放资源时出现异常: {ex.Message}");
            }
        }
    }
}
```

### 3.3 H2testw控制器示例
```csharp
// H2testwController.cs
using FlaUI.Core.AutomationElements;
using FlaUI.Core.Definitions;
using Microsoft.Extensions.Logging;
using System;
using System.Text.RegularExpressions;
using System.Threading;
using System.Threading.Tasks;

namespace MassStorageStableTestTool.Automation.Controllers
{
    public class H2testwController : BaseTestToolController
    {
        public override string ToolName => "H2testw";
        
        public H2testwController(ILogger<H2testwController> logger, TestToolConfig config) 
            : base(logger, config)
        {
        }
        
        public override async Task<TestResult> ExecuteTestAsync(TestConfiguration config, CancellationToken cancellationToken)
        {
            var result = new TestResult
            {
                ToolName = ToolName,
                StartTime = DateTime.Now
            };
            
            try
            {
                // 1. 启动应用程序
                await LaunchApplicationAsync();
                var mainWindow = await WaitForMainWindowAsync(TimeSpan.FromSeconds(30));
                
                // 2. 设置目标驱动器
                await SetTargetDriveAsync(mainWindow, config.TargetDrive);
                
                // 3. 开始测试
                await StartTestAsync(mainWindow);
                
                // 4. 监控测试进度
                await MonitorTestProgressAsync(mainWindow, cancellationToken);
                
                // 5. 解析结果
                var testData = await ParseResultsAsync(mainWindow);
                result.Data = testData;
                result.Success = true;
                
                _logger.LogInformation($"{ToolName} 测试完成");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, $"{ToolName} 测试失败");
            }
            finally
            {
                result.EndTime = DateTime.Now;
            }
            
            return result;
        }
        
        private async Task SetTargetDriveAsync(Window window, string targetDrive)
        {
            // 查找目标驱动器选择控件
            var driveComboBox = window.FindFirstDescendant(cf => 
                cf.ByControlType(ControlType.ComboBox).And(cf.ByName("Target")));
                
            if (driveComboBox != null)
            {
                await driveComboBox.AsComboBox().SelectAsync(targetDrive);
                _logger.LogInformation($"已选择目标驱动器: {targetDrive}");
            }
            else
            {
                throw new InvalidOperationException("未找到目标驱动器选择控件");
            }
        }
        
        private async Task StartTestAsync(Window window)
        {
            var writeButton = window.FindFirstDescendant(cf => 
                cf.ByControlType(ControlType.Button).And(cf.ByName("Write + Verify")));
                
            if (writeButton != null)
            {
                await writeButton.AsButton().InvokeAsync();
                _logger.LogInformation("已开始H2testw测试");
            }
            else
            {
                throw new InvalidOperationException("未找到开始测试按钮");
            }
        }
        
        private async Task MonitorTestProgressAsync(Window window, CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested)
            {
                // 检查是否完成
                var resultText = GetResultText(window);
                if (resultText.Contains("Test finished") || resultText.Contains("Error"))
                {
                    break;
                }
                
                await Task.Delay(5000, cancellationToken);
            }
        }
        
        private async Task<Dictionary<string, object>> ParseResultsAsync(Window window)
        {
            var resultText = GetResultText(window);
            var data = new Dictionary<string, object>();
            
            // 解析写入速度
            var writeSpeedMatch = Regex.Match(resultText, @"Writing speed: ([\d.]+) MByte/s");
            if (writeSpeedMatch.Success)
            {
                data["WriteSpeed"] = double.Parse(writeSpeedMatch.Groups[1].Value);
            }
            
            // 解析读取速度
            var readSpeedMatch = Regex.Match(resultText, @"Reading speed: ([\d.]+) MByte/s");
            if (readSpeedMatch.Success)
            {
                data["ReadSpeed"] = double.Parse(readSpeedMatch.Groups[1].Value);
            }
            
            // 解析测试结果
            data["TestPassed"] = resultText.Contains("Test finished without errors");
            
            return data;
        }
        
        private string GetResultText(Window window)
        {
            var resultTextBox = window.FindFirstDescendant(cf => 
                cf.ByControlType(ControlType.Edit).And(cf.ByName("Result")));
                
            return resultTextBox?.AsTextBox()?.Text ?? string.Empty;
        }
    }
}
```

---

## 4. 配置文件设置

### 4.1 appsettings.json
```json
{
  "TestTools": {
    "H2testw": {
      "ExecutablePath": "./third_party_tools/h2testw.exe",
      "WindowTitle": "H2testw",
      "Timeout": "02:00:00"
    },
    "CrystalDiskMark": {
      "ExecutablePath": "./third_party_tools/CrystalDiskMark.exe", 
      "WindowTitle": "CrystalDiskMark",
      "Timeout": "00:30:00"
    }
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  }
}
```

### 4.2 NLog.config
```xml
<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  
  <targets>
    <target xsi:type="File" name="fileTarget"
            fileName="logs/app-${shortdate}.log"
            layout="${longdate} ${level:uppercase=true} ${logger} ${message} ${exception:format=tostring}" />
  </targets>
  
  <rules>
    <logger name="*" minlevel="Info" writeTo="fileTarget" />
  </rules>
</nlog>
```

---

## 5. 依赖注入配置

### 5.1 服务注册 (App.xaml.cs)
```csharp
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using NLog.Extensions.Logging;

namespace MassStorageStableTestTool.UI
{
    public partial class App : Application
    {
        public IServiceProvider ServiceProvider { get; private set; }
        
        protected override void OnStartup(StartupEventArgs e)
        {
            var serviceCollection = new ServiceCollection();
            ConfigureServices(serviceCollection);
            ServiceProvider = serviceCollection.BuildServiceProvider();
            
            var mainWindow = ServiceProvider.GetRequiredService<MainWindow>();
            mainWindow.Show();
            
            base.OnStartup(e);
        }
        
        private void ConfigureServices(IServiceCollection services)
        {
            // 配置
            var configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json")
                .Build();
            services.AddSingleton<IConfiguration>(configuration);
            
            // 日志
            services.AddLogging(builder =>
            {
                builder.ClearProviders();
                builder.AddNLog();
            });
            
            // 业务服务
            services.AddSingleton<ITestOrchestrator, TestOrchestrator>();
            services.AddSingleton<IConfigurationService, ConfigurationService>();
            services.AddSingleton<IReportService, ReportService>();
            
            // 测试控制器
            services.AddTransient<H2testwController>();
            services.AddTransient<CrystalDiskMarkController>();
            
            // UI
            services.AddTransient<MainWindow>();
            services.AddTransient<MainViewModel>();
        }
    }
}
```

---

## 6. 第一个测试

### 6.1 创建简单测试
```csharp
// H2testwControllerTests.cs
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace MassStorageStableTestTool.Tests
{
    public class H2testwControllerTests
    {
        [Fact]
        public void IsToolAvailable_WhenFileExists_ReturnsTrue()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<H2testwController>>();
            var config = new TestToolConfig
            {
                ExecutablePath = "test_file.exe"
            };
            
            // 创建测试文件
            File.WriteAllText("test_file.exe", "test");
            
            var controller = new H2testwController(mockLogger.Object, config);
            
            // Act
            var result = controller.IsToolAvailable();
            
            // Assert
            Assert.True(result);
            
            // Cleanup
            File.Delete("test_file.exe");
        }
    }
}
```

### 6.2 运行测试
```powershell
# 构建解决方案
dotnet build

# 运行测试
dotnet test

# 运行应用程序
dotnet run --project MassStorageStableTestTool.UI
```

---

## 7. 调试技巧

### 7.1 FlaUI调试
```csharp
// 启用详细日志
_logger.LogDebug($"查找元素: {elementName}");

// 使用FlaUI Inspector工具查看UI结构
// 下载地址: https://github.com/FlaUI/FlaUI/releases

// 添加重试机制
private async Task<AutomationElement> FindElementWithRetryAsync(
    AutomationElement parent, 
    Func<ConditionFactory, ConditionBase> condition,
    int maxRetries = 5)
{
    for (int i = 0; i < maxRetries; i++)
    {
        var element = parent.FindFirstDescendant(condition);
        if (element != null) return element;
        
        await Task.Delay(1000);
    }
    
    throw new ElementNotFoundException("元素未找到");
}
```

### 7.2 常见问题解决
1. **UI元素找不到**: 使用FlaUI Inspector检查元素属性
2. **应用程序启动失败**: 检查路径和权限
3. **测试超时**: 增加超时时间或优化查找逻辑
4. **内存泄漏**: 确保正确释放FlaUI资源

---

## 8. 下一步

1. **完善H2testw控制器**: 处理更多边界情况
2. **添加CrystalDiskMark控制器**: 参考H2testw实现
3. **创建WPF主界面**: 实现基本的用户交互
4. **添加配置管理**: 支持动态配置修改
5. **实现报告生成**: 整合所有测试结果

这个快速开始指南提供了项目的基础框架，开发团队可以基于此快速开始开发工作。
