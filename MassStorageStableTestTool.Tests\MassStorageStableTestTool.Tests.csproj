﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0-windows</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="6.0.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.12.0" />
    <PackageReference Include="xunit" Version="2.9.2" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.8.2" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MassStorageStableTestTool.Core\MassStorageStableTestTool.Core.csproj" />
    <ProjectReference Include="..\MassStorageStableTestTool.Automation\MassStorageStableTestTool.Automation.csproj" />
    <ProjectReference Include="..\MassStorageStableTestTool.Reports\MassStorageStableTestTool.Reports.csproj" />
    <ProjectReference Include="..\MassStorageStableTestTool.UI\MassStorageStableTestTool.UI.csproj" />
  </ItemGroup>

</Project>
